#!/bin/bash

# ESS-HELM 稳定性和性能评估脚本
# 监控服务运行状态和资源使用情况，执行负载测试验证服务承载能力
# 版本: 1.0
# 日期: 2025-01-19

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 全局变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
NAMESPACE="ess-test"
RELEASE_NAME="ess-test"
LOG_FILE="$PROJECT_ROOT/stability-performance-$(date +%Y%m%d-%H%M%S).log"
MONITORING_DURATION=300  # 5分钟监控
LOAD_TEST_DURATION=60    # 1分钟负载测试

# 测试结果统计
TOTAL_ASSESSMENTS=0
PASSED_ASSESSMENTS=0
FAILED_ASSESSMENTS=0
WARNINGS=0

# 性能指标
declare -A PERFORMANCE_METRICS=(
    ["cpu_usage_threshold"]="80"      # CPU使用率阈值 (%)
    ["memory_usage_threshold"]="80"   # 内存使用率阈值 (%)
    ["response_time_threshold"]="2"   # 响应时间阈值 (秒)
    ["error_rate_threshold"]="5"      # 错误率阈值 (%)
)

# 日志函数
log() {
    echo -e "$1" | tee -a "$LOG_FILE"
}

log_info() {
    log "${BLUE}[INFO]${NC} $1"
}

log_success() {
    log "${GREEN}[PASS]${NC} $1"
    ((PASSED_ASSESSMENTS++))
}

log_error() {
    log "${RED}[FAIL]${NC} $1"
    ((FAILED_ASSESSMENTS++))
}

log_warning() {
    log "${YELLOW}[WARN]${NC} $1"
    ((WARNINGS++))
}

# 评估函数
run_assessment() {
    local assessment_name="$1"
    local assessment_command="$2"
    
    ((TOTAL_ASSESSMENTS++))
    log_info "执行评估: $assessment_name"
    
    if eval "$assessment_command" >> "$LOG_FILE" 2>&1; then
        log_success "$assessment_name"
        return 0
    else
        log_error "$assessment_name"
        return 1
    fi
}

# 检查部署状态
check_deployment_status() {
    log_info "=== 4. 稳定性和性能评估 ==="
    log_info "检查部署状态..."
    
    # 检查命名空间是否存在
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_error "测试命名空间 $NAMESPACE 不存在，请先运行部署过程验证"
        exit 1
    fi
    
    # 检查 Helm release 状态
    if ! helm list -n "$NAMESPACE" | grep -q "$RELEASE_NAME"; then
        log_error "Helm release $RELEASE_NAME 不存在，请先运行部署过程验证"
        exit 1
    fi
    
    log_success "部署状态检查通过"
}

# 监控服务运行状态
monitor_service_status() {
    log_info "监控服务运行状态 (持续 ${MONITORING_DURATION} 秒)..."
    
    local start_time
    start_time=$(date +%s)
    local end_time=$((start_time + MONITORING_DURATION))
    local check_interval=30
    
    local status_log="$PROJECT_ROOT/service-status-$(date +%Y%m%d-%H%M%S).log"
    
    while [ $(date +%s) -lt $end_time ]; do
        local current_time
        current_time=$(date '+%Y-%m-%d %H:%M:%S')
        
        echo "=== $current_time ===" >> "$status_log"
        
        # 检查 Pod 状态
        kubectl get pods -n "$NAMESPACE" --no-headers >> "$status_log" 2>&1
        
        # 检查服务状态
        kubectl get services -n "$NAMESPACE" --no-headers >> "$status_log" 2>&1
        
        # 检查最近的事件
        kubectl get events -n "$NAMESPACE" --sort-by='.lastTimestamp' | tail -5 >> "$status_log" 2>&1
        
        echo "" >> "$status_log"
        
        sleep $check_interval
    done
    
    log_success "服务状态监控完成，详细日志: $status_log"
    
    # 分析监控结果
    analyze_monitoring_results "$status_log"
}

# 分析监控结果
analyze_monitoring_results() {
    local status_log="$1"
    
    log_info "分析监控结果..."
    
    # 检查 Pod 重启次数
    local restart_count
    restart_count=$(kubectl get pods -n "$NAMESPACE" --no-headers | awk '{sum += $4} END {print sum+0}')
    
    if [ "$restart_count" -eq 0 ]; then
        log_success "监控期间无 Pod 重启"
    else
        log_warning "监控期间发现 $restart_count 次 Pod 重启"
    fi
    
    # 检查错误事件
    local error_events
    error_events=$(kubectl get events -n "$NAMESPACE" | grep -c "Warning\|Error" || echo "0")
    
    if [ "$error_events" -eq 0 ]; then
        log_success "监控期间无错误事件"
    else
        log_warning "监控期间发现 $error_events 个错误事件"
    fi
}

# 检查资源使用情况
check_resource_usage() {
    log_info "检查资源使用情况..."
    
    # 检查是否有 metrics-server
    if ! kubectl top nodes &> /dev/null; then
        log_warning "metrics-server 不可用，跳过资源使用检查"
        return 0
    fi
    
    # 检查节点资源使用
    log_info "节点资源使用情况:"
    kubectl top nodes | while read -r line; do
        if echo "$line" | grep -q "NAME"; then
            continue
        fi
        
        local node_name cpu_usage memory_usage
        node_name=$(echo "$line" | awk '{print $1}')
        cpu_usage=$(echo "$line" | awk '{print $3}' | sed 's/%//')
        memory_usage=$(echo "$line" | awk '{print $5}' | sed 's/%//')
        
        log_info "节点 $node_name: CPU ${cpu_usage}%, 内存 ${memory_usage}%"
        
        if [ "$cpu_usage" -gt "${PERFORMANCE_METRICS[cpu_usage_threshold]}" ]; then
            log_warning "节点 $node_name CPU 使用率过高: ${cpu_usage}%"
        fi
        
        if [ "$memory_usage" -gt "${PERFORMANCE_METRICS[memory_usage_threshold]}" ]; then
            log_warning "节点 $node_name 内存使用率过高: ${memory_usage}%"
        fi
    done
    
    # 检查 Pod 资源使用
    log_info "Pod 资源使用情况:"
    kubectl top pods -n "$NAMESPACE" | while read -r line; do
        if echo "$line" | grep -q "NAME"; then
            continue
        fi
        
        local pod_name cpu_usage memory_usage
        pod_name=$(echo "$line" | awk '{print $1}')
        cpu_usage=$(echo "$line" | awk '{print $2}')
        memory_usage=$(echo "$line" | awk '{print $3}')
        
        log_info "Pod $pod_name: CPU $cpu_usage, 内存 $memory_usage"
    done
    
    log_success "资源使用情况检查完成"
}

# 执行基础负载测试
execute_load_test() {
    log_info "执行基础负载测试 (持续 ${LOAD_TEST_DURATION} 秒)..."
    
    # 查找可测试的服务
    local test_services
    test_services=$(kubectl get services -n "$NAMESPACE" --no-headers | grep -v "ClusterIP.*<none>" | awk '{print $1}' || echo "")
    
    if [ -z "$test_services" ]; then
        log_warning "未找到可测试的服务端点"
        return 0
    fi
    
    echo "$test_services" | head -3 | while read -r service_name; do
        if [ -n "$service_name" ]; then
            log_info "对服务 $service_name 执行负载测试..."
            
            # 获取服务端口
            local port
            port=$(kubectl get service "$service_name" -n "$NAMESPACE" -o jsonpath='{.spec.ports[0].port}' 2>/dev/null || echo "")
            
            if [ -n "$port" ]; then
                # 启动端口转发
                kubectl port-forward -n "$NAMESPACE" "service/$service_name" "8080:$port" &
                local pf_pid=$!
                sleep 3
                
                # 执行简单的并发请求测试
                local success_count=0
                local total_requests=10
                local start_time end_time
                
                start_time=$(date +%s.%N)
                
                for i in $(seq 1 $total_requests); do
                    if curl -s --connect-timeout 5 --max-time 10 "http://localhost:8080" > /dev/null 2>&1; then
                        ((success_count++))
                    fi
                done
                
                end_time=$(date +%s.%N)
                
                # 计算性能指标
                local duration
                duration=$(echo "$end_time - $start_time" | bc -l 2>/dev/null || echo "1")
                local avg_response_time
                avg_response_time=$(echo "scale=2; $duration / $total_requests" | bc -l 2>/dev/null || echo "1")
                local success_rate
                success_rate=$(echo "scale=2; $success_count * 100 / $total_requests" | bc -l 2>/dev/null || echo "0")
                
                log_info "服务 $service_name 负载测试结果:"
                log_info "  - 总请求数: $total_requests"
                log_info "  - 成功请求数: $success_count"
                log_info "  - 成功率: ${success_rate}%"
                log_info "  - 平均响应时间: ${avg_response_time}s"
                
                # 评估性能指标
                if (( $(echo "$success_rate >= 95" | bc -l 2>/dev/null || echo "0") )); then
                    log_success "服务 $service_name 成功率正常"
                else
                    log_warning "服务 $service_name 成功率偏低: ${success_rate}%"
                fi
                
                if (( $(echo "$avg_response_time <= ${PERFORMANCE_METRICS[response_time_threshold]}" | bc -l 2>/dev/null || echo "0") )); then
                    log_success "服务 $service_name 响应时间正常"
                else
                    log_warning "服务 $service_name 响应时间偏高: ${avg_response_time}s"
                fi
                
                # 清理端口转发
                kill $pf_pid 2>/dev/null || true
                sleep 1
            else
                log_warning "服务 $service_name 无有效端口，跳过负载测试"
            fi
        fi
    done
    
    log_success "负载测试完成"
}

# 检查健康检查功能
verify_health_checks() {
    log_info "验证健康检查功能..."
    
    # 检查 Pod 的健康检查配置
    local pods
    pods=$(kubectl get pods -n "$NAMESPACE" --no-headers | awk '{print $1}' || echo "")
    
    echo "$pods" | while read -r pod_name; do
        if [ -n "$pod_name" ]; then
            # 检查是否配置了 livenessProbe
            local has_liveness
            has_liveness=$(kubectl get pod "$pod_name" -n "$NAMESPACE" -o jsonpath='{.spec.containers[*].livenessProbe}' 2>/dev/null || echo "")
            
            if [ -n "$has_liveness" ]; then
                log_success "Pod $pod_name 配置了存活探针"
            else
                log_info "Pod $pod_name 未配置存活探针"
            fi
            
            # 检查是否配置了 readinessProbe
            local has_readiness
            has_readiness=$(kubectl get pod "$pod_name" -n "$NAMESPACE" -o jsonpath='{.spec.containers[*].readinessProbe}' 2>/dev/null || echo "")
            
            if [ -n "$has_readiness" ]; then
                log_success "Pod $pod_name 配置了就绪探针"
            else
                log_info "Pod $pod_name 未配置就绪探针"
            fi
        fi
    done
}

# 检查日志和错误处理
check_logs_and_error_handling() {
    log_info "检查日志输出和错误处理机制..."
    
    # 检查所有 Pod 的日志
    local pods
    pods=$(kubectl get pods -n "$NAMESPACE" --no-headers | awk '{print $1}' || echo "")
    
    local total_errors=0
    
    echo "$pods" | while read -r pod_name; do
        if [ -n "$pod_name" ]; then
            log_info "分析 Pod $pod_name 日志..."
            
            # 获取最近的日志并分析错误
            local error_count
            error_count=$(kubectl logs "$pod_name" -n "$NAMESPACE" --tail=100 2>/dev/null | \
                grep -i -c "error\|exception\|failed\|panic\|fatal" || echo "0")
            
            if [ "$error_count" -eq 0 ]; then
                log_success "Pod $pod_name 无明显错误日志"
            else
                log_warning "Pod $pod_name 发现 $error_count 条错误日志"
                total_errors=$((total_errors + error_count))
            fi
            
            # 检查日志格式和结构
            local log_lines
            log_lines=$(kubectl logs "$pod_name" -n "$NAMESPACE" --tail=10 2>/dev/null | wc -l || echo "0")
            
            if [ "$log_lines" -gt 0 ]; then
                log_success "Pod $pod_name 日志输出正常"
            else
                log_warning "Pod $pod_name 无日志输出"
            fi
        fi
    done
    
    log_info "总错误日志数: $total_errors"
}

# 主函数
main() {
    log_info "开始 ESS-HELM 稳定性和性能评估"
    log_info "测试时间: $(date)"
    log_info "命名空间: $NAMESPACE"
    log_info "监控持续时间: ${MONITORING_DURATION} 秒"
    log_info "负载测试持续时间: ${LOAD_TEST_DURATION} 秒"
    log_info "日志文件: $LOG_FILE"
    echo ""
    
    # 执行稳定性和性能评估
    check_deployment_status
    monitor_service_status
    check_resource_usage
    execute_load_test
    verify_health_checks
    check_logs_and_error_handling
    
    # 生成评估摘要
    echo ""
    log_info "=== 稳定性和性能评估摘要 ==="
    log_info "总评估数: $TOTAL_ASSESSMENTS"
    log_info "通过评估: $PASSED_ASSESSMENTS"
    log_info "失败评估: $FAILED_ASSESSMENTS"
    log_info "警告数量: $WARNINGS"
    
    if [ "$FAILED_ASSESSMENTS" -eq 0 ]; then
        log_success "稳定性和性能评估通过！"
        exit 0
    else
        log_error "部分稳定性和性能评估失败，请查看详细日志: $LOG_FILE"
        exit 1
    fi
}

# 执行主函数
main "$@"
