# ESS-HELM 测试环境清理指南

## 📋 概述

本指南提供了清理 ESS-HELM 部署验证测试环境的详细说明，确保测试环境能够安全、彻底地恢复到干净状态。

**清理工具版本**: v1.0  
**支持的清理范围**: Kubernetes 资源、本地文件、后台进程、系统状态  

---

## 🧹 清理工具概览

### 1. 完整清理工具
**脚本**: `./scripts/cleanup-test-environment.sh`  
**用途**: 全面彻底的环境清理，适用于重要测试后或环境重置  

### 2. 快速清理工具
**脚本**: `./scripts/quick-cleanup.sh`  
**用途**: 日常开发中的快速清理，适用于频繁测试场景  

---

## 🚀 快速开始

### 方法一：快速清理（推荐日常使用）

```bash
# 交互式快速清理
./scripts/quick-cleanup.sh

# 强制快速清理（无确认提示）
./scripts/quick-cleanup.sh -f

# 预览清理操作
./scripts/quick-cleanup.sh --dry-run

# 保留日志文件的快速清理
./scripts/quick-cleanup.sh -k
```

### 方法二：完整清理（推荐重要场景）

```bash
# 交互式完整清理
./scripts/cleanup-test-environment.sh

# 强制完整清理
./scripts/cleanup-test-environment.sh -f

# 清理所有 ESS 相关命名空间
./scripts/cleanup-test-environment.sh -a

# 清理指定命名空间
./scripts/cleanup-test-environment.sh -n custom-test

# 预览完整清理计划
./scripts/cleanup-test-environment.sh --dry-run
```

---

## 📊 清理范围对比

| 清理项目 | 快速清理 | 完整清理 | 说明 |
|---------|---------|---------|------|
| **Kubernetes 资源** | | | |
| 删除 ess-test 命名空间 | ✅ | ✅ | 默认测试命名空间 |
| 删除自定义命名空间 | ❌ | ✅ | 需要指定参数 |
| 清理 Helm releases | ✅ | ✅ | 自动检测并删除 |
| 强制删除残留资源 | ❌ | ✅ | PVC、Secret 等 |
| **本地文件** | | | |
| 清理测试日志 | ✅ | ✅ | 可选保留 |
| 清理验证报告 | 部分 | ✅ | 可选保留 |
| 清理临时文件 | ✅ | ✅ | 完全清理 |
| **后台进程** | | | |
| 终止端口转发 | ✅ | ✅ | kubectl port-forward |
| 终止测试进程 | ✅ | ✅ | 验证脚本进程 |
| 清理僵尸进程 | ❌ | ✅ | 深度清理 |
| **验证和报告** | | | |
| 清理结果验证 | ✅ | ✅ | 自动验证 |
| 生成清理报告 | ❌ | ✅ | 详细报告 |

---

## 🔧 高级清理选项

### 完整清理工具高级选项

```bash
# 清理多个指定命名空间
./scripts/cleanup-test-environment.sh -n test1 -n test2 -n test3

# 保留日志和报告文件
./scripts/cleanup-test-environment.sh --keep-logs --keep-reports

# 详细输出模式
./scripts/cleanup-test-environment.sh -v

# 组合选项使用
./scripts/cleanup-test-environment.sh -v -f --keep-logs
```

### 环境变量配置

```bash
# 设置自定义超时时间
export CLEANUP_TIMEOUT="600"  # 10分钟

# 设置自定义命名空间前缀
export ESS_NAMESPACE_PREFIX="my-ess"

# 启用调试模式
export CLEANUP_DEBUG="true"
```

---

## 🎯 使用场景

### 场景一：日常开发测试
**推荐**: 快速清理工具

```bash
# 开发过程中频繁测试
./scripts/master-validation.sh -p 1  # 运行单个测试
./scripts/quick-cleanup.sh -f        # 快速清理
./scripts/master-validation.sh -p 2  # 运行下个测试
```

### 场景二：完整验证测试
**推荐**: 完整清理工具

```bash
# 重要版本发布前的完整测试
./scripts/master-validation.sh                    # 完整验证
./scripts/cleanup-test-environment.sh -v          # 详细清理
```

### 场景三：多环境测试
**推荐**: 指定命名空间清理

```bash
# 测试不同配置
./scripts/master-validation.sh -n test-config-1
./scripts/master-validation.sh -n test-config-2

# 清理指定环境
./scripts/cleanup-test-environment.sh -n test-config-1 -n test-config-2
```

### 场景四：CI/CD 集成
**推荐**: 自动化清理

```bash
# CI/CD 管道中的清理
./scripts/cleanup-test-environment.sh -f --keep-reports
```

---

## 🔍 故障排除

### 常见问题

#### 1. 命名空间删除卡住
```bash
# 检查命名空间状态
kubectl get namespace ess-test -o yaml

# 强制删除命名空间
kubectl patch namespace ess-test -p '{"metadata":{"finalizers":null}}'
kubectl delete namespace ess-test --force --grace-period=0
```

#### 2. Helm release 删除失败
```bash
# 检查 Helm release 状态
helm list -n ess-test

# 强制删除 Helm release
helm uninstall <release-name> -n ess-test --no-hooks
```

#### 3. 进程无法终止
```bash
# 查找相关进程
ps aux | grep -E "(kubectl|validation|test)"

# 强制终止进程
sudo kill -9 <pid>
```

#### 4. 文件删除权限问题
```bash
# 检查文件权限
ls -la *validation*.log

# 修改权限后删除
chmod 644 *validation*.log
rm -f *validation*.log
```

### 手动清理步骤

如果自动清理失败，可以按以下步骤手动清理：

```bash
# 1. 手动清理 Kubernetes 资源
kubectl delete namespace ess-test --force --grace-period=0
helm list --all-namespaces | grep ess | awk '{print $1, $2}' | xargs -n2 helm uninstall

# 2. 手动清理本地文件
find . -name "*validation*.log" -delete
find . -name "*test*.log" -delete
find . -name "ESS-HELM-VALIDATION-REPORT-*.md" -delete

# 3. 手动清理进程
pkill -f "kubectl.*port-forward"
pkill -f "validation.*test"

# 4. 验证清理结果
kubectl get namespaces | grep ess
ps aux | grep -E "(kubectl|validation)" | grep -v grep
ls -la *.log
```

---

## 📈 清理性能

### 预期清理时间

| 清理类型 | 快速清理 | 完整清理 |
|---------|---------|---------|
| Kubernetes 资源 | 30-60秒 | 2-5分钟 |
| 本地文件 | 5-10秒 | 10-30秒 |
| 后台进程 | 5-10秒 | 10-20秒 |
| 验证和报告 | 10-20秒 | 30-60秒 |
| **总计** | **1-2分钟** | **3-7分钟** |

### 资源使用

- **CPU 使用**: 低 (< 10%)
- **内存使用**: 最小 (< 100MB)
- **网络使用**: 仅 Kubernetes API 调用
- **磁盘 I/O**: 文件删除操作

---

## 🛡️ 安全注意事项

### 清理前检查

1. **确认环境**: 确保在正确的 Kubernetes 集群上操作
2. **备份重要数据**: 如有重要测试数据，请先备份
3. **检查依赖**: 确保没有其他应用依赖测试资源
4. **权限验证**: 确保有足够的权限执行清理操作

### 安全措施

- ✅ 清理脚本包含确认提示（除非使用 -f 参数）
- ✅ 支持预览模式（--dry-run）查看清理计划
- ✅ 详细日志记录所有清理操作
- ✅ 清理结果验证确保操作成功
- ✅ 错误处理和回滚机制

---

## 📚 最佳实践

### 清理频率建议

- **开发阶段**: 每次测试后使用快速清理
- **测试阶段**: 每日结束时使用完整清理
- **发布前**: 重要测试后使用完整清理
- **维护期**: 定期使用完整清理检查环境

### 自动化建议

```bash
# 创建清理别名
alias ess-clean='./scripts/quick-cleanup.sh -f'
alias ess-clean-full='./scripts/cleanup-test-environment.sh -f'

# 添加到 .bashrc 或 .zshrc
echo "alias ess-clean='cd /path/to/ess-helm-deployment-package && ./scripts/quick-cleanup.sh -f'" >> ~/.bashrc
```

### 集成到工作流

```bash
# 测试前清理
./scripts/quick-cleanup.sh -f

# 执行测试
./scripts/master-validation.sh

# 测试后清理
./scripts/cleanup-test-environment.sh -f
```

---

## 📞 支持和帮助

### 获取帮助

```bash
# 查看清理脚本帮助
./scripts/cleanup-test-environment.sh --help
./scripts/quick-cleanup.sh --help

# 查看清理状态
kubectl get namespaces | grep ess
helm list --all-namespaces | grep ess
```

### 相关文档

- [部署验证指南](./COMPREHENSIVE-VALIDATION-GUIDE.md)
- [部署状态报告](./DEPLOYMENT-STATUS.md)
- [故障排除指南](./DEPLOYMENT-FIX-GUIDE.md)

---

## 🎉 结论

通过使用这套清理工具，您可以：

1. **确保环境干净**: 彻底清理测试残留，避免干扰后续测试
2. **提高效率**: 自动化清理流程，节省手动操作时间
3. **降低风险**: 安全的清理机制，避免误删重要资源
4. **标准化流程**: 为团队提供统一的清理标准

**建议**: 将清理操作集成到您的测试工作流中，确保每次测试都在干净的环境中进行。

---

**文档版本**: 1.0  
**最后更新**: 2025-01-19  
**维护者**: ESS-HELM 验证团队
