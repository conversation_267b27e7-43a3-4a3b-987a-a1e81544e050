#!/bin/bash

# ESS-HELM 部署包验证脚本
# 版本: v1.0
# 功能: 验证部署包的完整性和正确性
# 作者: ESS-HELM 部署团队
# 日期: 2025-06-20

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PACKAGE_NAME="ESS-HELM 一键部署系统"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

info() {
    log "${BLUE}[信息]${NC} $1"
}

success() {
    log "${GREEN}[成功]${NC} $1"
}

warning() {
    log "${YELLOW}[警告]${NC} $1"
}

error() {
    log "${RED}[错误]${NC} $1"
}

# 显示验证开始信息
show_validation_start() {
    clear
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                ESS-HELM 部署包完整性验证                      ║"
    echo "║                                                              ║"
    echo "║  验证项目: 文件完整性 + 权限检查 + 配置验证                    ║"
    echo "║  基于版本: ESS-HELM 25.6.2 官方稳定版                        ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo
}

# 验证必需文件
validate_required_files() {
    info "验证必需文件..."
    
    local required_files=(
        "setup.sh"
        "README.md"
        "scripts/external.sh"
        "scripts/internal.sh"
        "scripts/admin.sh"
        "scripts/router-wan-ip-detector.sh"
        "scripts/virtual-public-ip-route-manager.sh"
        "charts/matrix-stack/values.yaml"
        "charts/matrix-stack/values-router-wan-ip-detection.yaml"
        "charts/matrix-stack/values-virtual-public-ip-routing.yaml"
        "charts/matrix-stack/values-internal-server.yaml"
        "configs/external-server-nginx.conf"
        "docs/deployment-guide.md"
        "docs/admin-guide.md"
        "docs/troubleshooting.md"
    )
    
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if [[ -f "$SCRIPT_DIR/$file" ]]; then
            success "✓ $file"
        else
            error "✗ $file (缺失)"
            missing_files+=("$file")
        fi
    done
    
    if [[ ${#missing_files[@]} -eq 0 ]]; then
        success "所有必需文件验证通过"
        return 0
    else
        error "发现 ${#missing_files[@]} 个缺失文件"
        return 1
    fi
}

# 验证目录结构
validate_directory_structure() {
    info "验证目录结构..."
    
    local required_dirs=(
        "scripts"
        "charts"
        "charts/matrix-stack"
        "configs"
        "docs"
    )
    
    local missing_dirs=()
    
    for dir in "${required_dirs[@]}"; do
        if [[ -d "$SCRIPT_DIR/$dir" ]]; then
            success "✓ $dir/"
        else
            error "✗ $dir/ (缺失)"
            missing_dirs+=("$dir")
        fi
    done
    
    if [[ ${#missing_dirs[@]} -eq 0 ]]; then
        success "目录结构验证通过"
        return 0
    else
        error "发现 ${#missing_dirs[@]} 个缺失目录"
        return 1
    fi
}

# 验证脚本权限
validate_script_permissions() {
    info "验证脚本执行权限..."
    
    local script_files=(
        "setup.sh"
        "scripts/external.sh"
        "scripts/internal.sh"
        "scripts/admin.sh"
        "scripts/router-wan-ip-detector.sh"
        "scripts/virtual-public-ip-route-manager.sh"
    )
    
    local permission_issues=()
    
    for script in "${script_files[@]}"; do
        if [[ -f "$SCRIPT_DIR/$script" ]]; then
            if [[ -x "$SCRIPT_DIR/$script" ]]; then
                success "✓ $script (可执行)"
            else
                warning "⚠ $script (不可执行)"
                permission_issues+=("$script")
            fi
        fi
    done
    
    if [[ ${#permission_issues[@]} -eq 0 ]]; then
        success "脚本权限验证通过"
        return 0
    else
        warning "发现 ${#permission_issues[@]} 个权限问题，正在修复..."
        for script in "${permission_issues[@]}"; do
            chmod +x "$SCRIPT_DIR/$script"
            success "已修复 $script 权限"
        done
        return 0
    fi
}

# 验证配置文件语法
validate_configuration_syntax() {
    info "验证配置文件语法..."
    
    local yaml_files=(
        "charts/matrix-stack/values.yaml"
        "charts/matrix-stack/values-router-wan-ip-detection.yaml"
        "charts/matrix-stack/values-virtual-public-ip-routing.yaml"
        "charts/matrix-stack/values-internal-server.yaml"
    )
    
    local syntax_errors=()
    
    for yaml_file in "${yaml_files[@]}"; do
        if [[ -f "$SCRIPT_DIR/$yaml_file" ]]; then
            # 简单的YAML语法检查
            if python3 -c "import yaml; yaml.safe_load(open('$SCRIPT_DIR/$yaml_file'))" 2>/dev/null; then
                success "✓ $yaml_file (语法正确)"
            else
                error "✗ $yaml_file (语法错误)"
                syntax_errors+=("$yaml_file")
            fi
        fi
    done
    
    if [[ ${#syntax_errors[@]} -eq 0 ]]; then
        success "配置文件语法验证通过"
        return 0
    else
        error "发现 ${#syntax_errors[@]} 个语法错误"
        return 1
    fi
}

# 验证脚本内容
validate_script_content() {
    info "验证脚本内容完整性..."
    
    local validation_results=()
    
    # 检查setup.sh
    if [[ -f "$SCRIPT_DIR/setup.sh" ]]; then
        if grep -q "show_main_menu" "$SCRIPT_DIR/setup.sh"; then
            success "✓ setup.sh 包含主菜单功能"
        else
            error "✗ setup.sh 缺少主菜单功能"
            validation_results+=("setup.sh")
        fi
    fi
    
    # 检查external.sh
    if [[ -f "$SCRIPT_DIR/scripts/external.sh" ]]; then
        if grep -q "routerWanIpDetection" "$SCRIPT_DIR/scripts/external.sh"; then
            success "✓ external.sh 包含Router WAN IP检测功能"
        else
            error "✗ external.sh 缺少Router WAN IP检测功能"
            validation_results+=("external.sh")
        fi
    fi
    
    # 检查admin.sh
    if [[ -f "$SCRIPT_DIR/scripts/admin.sh" ]]; then
        if grep -q "user_management_menu" "$SCRIPT_DIR/scripts/admin.sh"; then
            success "✓ admin.sh 包含用户管理功能"
        else
            error "✗ admin.sh 缺少用户管理功能"
            validation_results+=("admin.sh")
        fi
    fi
    
    if [[ ${#validation_results[@]} -eq 0 ]]; then
        success "脚本内容验证通过"
        return 0
    else
        error "发现 ${#validation_results[@]} 个内容问题"
        return 1
    fi
}

# 验证文档完整性
validate_documentation() {
    info "验证文档完整性..."
    
    local doc_files=(
        "README.md"
        "docs/deployment-guide.md"
        "docs/admin-guide.md"
        "docs/troubleshooting.md"
    )
    
    local doc_issues=()
    
    for doc in "${doc_files[@]}"; do
        if [[ -f "$SCRIPT_DIR/$doc" ]]; then
            local word_count=$(wc -w < "$SCRIPT_DIR/$doc")
            if [[ $word_count -gt 100 ]]; then
                success "✓ $doc ($word_count 词)"
            else
                warning "⚠ $doc (内容较少: $word_count 词)"
                doc_issues+=("$doc")
            fi
        fi
    done
    
    if [[ ${#doc_issues[@]} -eq 0 ]]; then
        success "文档完整性验证通过"
        return 0
    else
        warning "发现 ${#doc_issues[@]} 个文档问题"
        return 0
    fi
}

# 生成验证报告
generate_validation_report() {
    local total_checks=6
    local passed_checks=0
    
    echo
    echo -e "${CYAN}验证报告${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # 执行所有验证
    validate_directory_structure && ((passed_checks++))
    validate_required_files && ((passed_checks++))
    validate_script_permissions && ((passed_checks++))
    validate_configuration_syntax && ((passed_checks++))
    validate_script_content && ((passed_checks++))
    validate_documentation && ((passed_checks++))
    
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "验证结果: ${passed_checks}/${total_checks} 项通过"
    
    if [[ $passed_checks -eq $total_checks ]]; then
        echo -e "${GREEN}✅ 部署包验证完全通过！${NC}"
        echo -e "${GREEN}🚀 可以安全地进行部署${NC}"
        return 0
    elif [[ $passed_checks -ge $((total_checks - 1)) ]]; then
        echo -e "${YELLOW}⚠️ 部署包基本完整，有轻微问题${NC}"
        echo -e "${YELLOW}🔧 建议修复后再部署${NC}"
        return 1
    else
        echo -e "${RED}❌ 部署包存在严重问题${NC}"
        echo -e "${RED}🛠️ 必须修复后才能部署${NC}"
        return 2
    fi
}

# 显示使用说明
show_usage_info() {
    echo
    echo -e "${CYAN}部署包使用说明${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "${WHITE}一键部署命令:${NC}"
    echo -e "  ${GREEN}./setup.sh${NC}"
    echo
    echo -e "${WHITE}管理现有部署:${NC}"
    echo -e "  ${GREEN}./scripts/admin.sh${NC}"
    echo
    echo -e "${WHITE}查看详细文档:${NC}"
    echo -e "  ${GREEN}cat docs/deployment-guide.md${NC}"
    echo -e "  ${GREEN}cat docs/admin-guide.md${NC}"
    echo -e "  ${GREEN}cat docs/troubleshooting.md${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
}

# 主函数
main() {
    show_validation_start
    
    info "开始验证 $PACKAGE_NAME 部署包..."
    echo
    
    if generate_validation_report; then
        show_usage_info
        exit 0
    else
        echo
        error "验证失败，请检查并修复问题后重新验证"
        exit 1
    fi
}

# 启动主程序
main "$@"
