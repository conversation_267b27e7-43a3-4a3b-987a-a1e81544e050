#!/bin/bash

# ESS-HELM 全面部署验证和服务稳定性测试脚本
# 基于官方 Kubernetes 和 Helm 最佳实践
# 版本: 1.0
# 日期: 2025-01-19

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 全局变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CHART_PATH="$PROJECT_ROOT/charts/matrix-stack"
VALUES_FILE="$PROJECT_ROOT/charts/matrix-stack/user_values/minimal-values.yaml"
NAMESPACE="ess-test"
RELEASE_NAME="ess-test"
LOG_FILE="$PROJECT_ROOT/deployment-validation-$(date +%Y%m%d-%H%M%S).log"
REPORT_FILE="$PROJECT_ROOT/deployment-validation-report-$(date +%Y%m%d-%H%M%S).md"

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
WARNINGS=0

# 日志函数
log() {
    echo -e "$1" | tee -a "$LOG_FILE"
}

log_info() {
    log "${BLUE}[INFO]${NC} $1"
}

log_success() {
    log "${GREEN}[PASS]${NC} $1"
    ((PASSED_TESTS++))
}

log_error() {
    log "${RED}[FAIL]${NC} $1"
    ((FAILED_TESTS++))
}

log_warning() {
    log "${YELLOW}[WARN]${NC} $1"
    ((WARNINGS++))
}

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    ((TOTAL_TESTS++))
    log_info "执行测试: $test_name"
    
    if eval "$test_command" >> "$LOG_FILE" 2>&1; then
        log_success "$test_name"
        return 0
    else
        log_error "$test_name"
        return 1
    fi
}

# 检查必需工具
check_prerequisites() {
    log_info "=== 1. 部署前检查验证 ==="
    log_info "检查必需工具..."
    
    local tools=("kubectl" "helm" "curl" "jq")
    for tool in "${tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            local version
            case "$tool" in
                "kubectl") version=$(kubectl version --client --short 2>/dev/null | grep -o 'v[0-9.]*' || echo "unknown") ;;
                "helm") version=$(helm version --short 2>/dev/null | grep -o 'v[0-9.]*' || echo "unknown") ;;
                "curl") version=$(curl --version 2>/dev/null | head -n1 | grep -o '[0-9.]*' | head -n1 || echo "unknown") ;;
                "jq") version=$(jq --version 2>/dev/null | grep -o '[0-9.]*' || echo "unknown") ;;
            esac
            log_success "$tool 已安装 (版本: $version)"
        else
            log_error "$tool 未安装"
        fi
    done
}

# 验证 Kubernetes 集群连接
check_cluster_connection() {
    log_info "验证 Kubernetes 集群连接..."
    
    if kubectl cluster-info &> /dev/null; then
        local cluster_version
        cluster_version=$(kubectl version --short 2>/dev/null | grep "Server Version" | grep -o 'v[0-9.]*' || echo "unknown")
        log_success "Kubernetes 集群连接正常 (服务器版本: $cluster_version)"
        
        # 检查节点状态
        local ready_nodes
        ready_nodes=$(kubectl get nodes --no-headers | grep -c "Ready" || echo "0")
        local total_nodes
        total_nodes=$(kubectl get nodes --no-headers | wc -l || echo "0")
        log_info "集群节点状态: $ready_nodes/$total_nodes 节点就绪"
        
        if [ "$ready_nodes" -eq "$total_nodes" ] && [ "$total_nodes" -gt 0 ]; then
            log_success "所有集群节点状态正常"
        else
            log_warning "部分集群节点未就绪"
        fi
    else
        log_error "无法连接到 Kubernetes 集群"
    fi
}

# 验证 Helm Chart 语法
validate_helm_syntax() {
    log_info "验证 Helm Chart 语法和结构..."
    
    # 检查 Chart.yaml
    if [ -f "$CHART_PATH/Chart.yaml" ]; then
        log_success "Chart.yaml 文件存在"
        
        # 验证 Chart.yaml 语法
        if helm show chart "$CHART_PATH" &> /dev/null; then
            log_success "Chart.yaml 语法正确"
        else
            log_error "Chart.yaml 语法错误"
        fi
    else
        log_error "Chart.yaml 文件不存在"
    fi
    
    # 检查模板目录
    if [ -d "$CHART_PATH/templates" ]; then
        log_success "templates 目录存在"
        
        # 统计模板文件数量
        local template_count
        template_count=$(find "$CHART_PATH/templates" -name "*.yaml" -o -name "*.yml" | wc -l)
        log_info "发现 $template_count 个模板文件"
    else
        log_error "templates 目录不存在"
    fi
    
    # 验证 values.yaml
    if [ -f "$CHART_PATH/values.yaml" ]; then
        log_success "values.yaml 文件存在"
    else
        log_error "values.yaml 文件不存在"
    fi
    
    # 验证用户配置文件
    if [ -f "$VALUES_FILE" ]; then
        log_success "用户配置文件存在: $(basename "$VALUES_FILE")"
    else
        log_error "用户配置文件不存在: $VALUES_FILE"
    fi
}

# 验证 Helm 模板渲染
validate_helm_templates() {
    log_info "验证 Helm 模板渲染..."
    
    # 使用 --dry-run 验证模板
    if helm template "$RELEASE_NAME" "$CHART_PATH" \
        --values "$VALUES_FILE" \
        --namespace "$NAMESPACE" \
        --dry-run > /dev/null 2>&1; then
        log_success "Helm 模板渲染成功"
    else
        log_error "Helm 模板渲染失败"
        log_info "运行以下命令查看详细错误:"
        log_info "helm template $RELEASE_NAME $CHART_PATH --values $VALUES_FILE --namespace $NAMESPACE --dry-run"
    fi
    
    # 验证 Kubernetes 资源语法
    if helm template "$RELEASE_NAME" "$CHART_PATH" \
        --values "$VALUES_FILE" \
        --namespace "$NAMESPACE" \
        --dry-run | kubectl apply --dry-run=client -f - &> /dev/null; then
        log_success "Kubernetes 资源语法验证通过"
    else
        log_error "Kubernetes 资源语法验证失败"
    fi
}

# 检查镜像可用性
check_image_availability() {
    log_info "检查容器镜像可用性..."
    
    # 渲染模板并提取镜像列表
    local images
    images=$(helm template "$RELEASE_NAME" "$CHART_PATH" \
        --values "$VALUES_FILE" \
        --namespace "$NAMESPACE" \
        --dry-run 2>/dev/null | \
        grep -E "^\s*image:" | \
        sed 's/.*image: *//g' | \
        sed 's/["\x27]//g' | \
        sort -u || echo "")
    
    if [ -n "$images" ]; then
        log_info "发现以下容器镜像:"
        echo "$images" | while read -r image; do
            if [ -n "$image" ]; then
                log_info "  - $image"
                # 注意: 实际环境中可以使用 docker pull 或 skopeo inspect 验证镜像
                # 这里仅记录镜像信息
            fi
        done
        log_success "镜像列表提取完成"
    else
        log_warning "未能提取到镜像列表"
    fi
}

# 验证 RBAC 配置
validate_rbac_config() {
    log_info "验证 RBAC 权限和服务账户配置..."
    
    # 检查是否包含 ServiceAccount 资源
    local sa_count
    sa_count=$(helm template "$RELEASE_NAME" "$CHART_PATH" \
        --values "$VALUES_FILE" \
        --namespace "$NAMESPACE" \
        --dry-run 2>/dev/null | \
        grep -c "kind: ServiceAccount" || echo "0")
    
    if [ "$sa_count" -gt 0 ]; then
        log_success "发现 $sa_count 个 ServiceAccount 配置"
    else
        log_info "未发现 ServiceAccount 配置（可能使用默认账户）"
    fi
    
    # 检查是否包含 Role/ClusterRole 资源
    local role_count
    role_count=$(helm template "$RELEASE_NAME" "$CHART_PATH" \
        --values "$VALUES_FILE" \
        --namespace "$NAMESPACE" \
        --dry-run 2>/dev/null | \
        grep -c -E "kind: (Role|ClusterRole)" || echo "0")
    
    if [ "$role_count" -gt 0 ]; then
        log_success "发现 $role_count 个 Role/ClusterRole 配置"
    else
        log_info "未发现 Role/ClusterRole 配置"
    fi
    
    # 检查是否包含 RoleBinding/ClusterRoleBinding 资源
    local binding_count
    binding_count=$(helm template "$RELEASE_NAME" "$CHART_PATH" \
        --values "$VALUES_FILE" \
        --namespace "$NAMESPACE" \
        --dry-run 2>/dev/null | \
        grep -c -E "kind: (RoleBinding|ClusterRoleBinding)" || echo "0")
    
    if [ "$binding_count" -gt 0 ]; then
        log_success "发现 $binding_count 个 RoleBinding/ClusterRoleBinding 配置"
    else
        log_info "未发现 RoleBinding/ClusterRoleBinding 配置"
    fi
}

# 主函数
main() {
    log_info "开始 ESS-HELM 全面部署验证测试"
    log_info "测试时间: $(date)"
    log_info "项目路径: $PROJECT_ROOT"
    log_info "Chart 路径: $CHART_PATH"
    log_info "配置文件: $VALUES_FILE"
    log_info "日志文件: $LOG_FILE"
    log_info "报告文件: $REPORT_FILE"
    echo ""
    
    # 执行检查
    check_prerequisites
    check_cluster_connection
    validate_helm_syntax
    validate_helm_templates
    check_image_availability
    validate_rbac_config
    
    # 生成测试摘要
    echo ""
    log_info "=== 测试摘要 ==="
    log_info "总测试数: $TOTAL_TESTS"
    log_info "通过测试: $PASSED_TESTS"
    log_info "失败测试: $FAILED_TESTS"
    log_info "警告数量: $WARNINGS"
    
    if [ "$FAILED_TESTS" -eq 0 ]; then
        log_success "所有部署前检查验证通过！"
        exit 0
    else
        log_error "部分检查验证失败，请查看详细日志: $LOG_FILE"
        exit 1
    fi
}

# 执行主函数
main "$@"
