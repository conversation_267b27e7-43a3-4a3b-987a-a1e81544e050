#!/bin/bash

# ESS-HELM 部署过程验证脚本
# 在测试环境中执行完整的 Helm 部署流程验证
# 版本: 1.0
# 日期: 2025-01-19

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 全局变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CHART_PATH="$PROJECT_ROOT/charts/matrix-stack"
VALUES_FILE="$PROJECT_ROOT/charts/matrix-stack/user_values/minimal-values.yaml"
NAMESPACE="ess-test"
RELEASE_NAME="ess-test"
LOG_FILE="$PROJECT_ROOT/deployment-process-$(date +%Y%m%d-%H%M%S).log"
TIMEOUT="15m"

# 测试结果统计
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNINGS=0

# 日志函数
log() {
    echo -e "$1" | tee -a "$LOG_FILE"
}

log_info() {
    log "${BLUE}[INFO]${NC} $1"
}

log_success() {
    log "${GREEN}[PASS]${NC} $1"
    ((PASSED_CHECKS++))
}

log_error() {
    log "${RED}[FAIL]${NC} $1"
    ((FAILED_CHECKS++))
}

log_warning() {
    log "${YELLOW}[WARN]${NC} $1"
    ((WARNINGS++))
}

# 检查函数
run_check() {
    local check_name="$1"
    local check_command="$2"
    
    ((TOTAL_CHECKS++))
    log_info "执行检查: $check_name"
    
    if eval "$check_command" >> "$LOG_FILE" 2>&1; then
        log_success "$check_name"
        return 0
    else
        log_error "$check_name"
        return 1
    fi
}

# 清理函数
cleanup() {
    log_info "清理测试环境..."
    
    # 删除 Helm release
    if helm list -n "$NAMESPACE" | grep -q "$RELEASE_NAME"; then
        log_info "删除 Helm release: $RELEASE_NAME"
        helm uninstall "$RELEASE_NAME" -n "$NAMESPACE" --timeout "$TIMEOUT" || true
    fi
    
    # 等待资源清理
    log_info "等待资源清理完成..."
    sleep 30
    
    # 删除命名空间
    if kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_info "删除命名空间: $NAMESPACE"
        kubectl delete namespace "$NAMESPACE" --timeout=300s || true
    fi
    
    log_info "清理完成"
}

# 信号处理
trap cleanup EXIT

# 准备测试环境
prepare_test_environment() {
    log_info "=== 2. 部署过程验证 ==="
    log_info "准备测试环境..."
    
    # 清理可能存在的旧环境
    cleanup
    
    # 创建测试命名空间
    log_info "创建测试命名空间: $NAMESPACE"
    if kubectl create namespace "$NAMESPACE"; then
        log_success "命名空间创建成功"
    else
        log_error "命名空间创建失败"
        return 1
    fi
    
    # 验证命名空间状态
    if kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_success "命名空间验证成功"
    else
        log_error "命名空间验证失败"
        return 1
    fi
}

# 执行 Helm 部署
execute_helm_deployment() {
    log_info "执行 Helm 部署..."
    
    # 首先进行 dry-run 验证
    log_info "执行 Helm dry-run 验证..."
    if helm install "$RELEASE_NAME" "$CHART_PATH" \
        --namespace "$NAMESPACE" \
        --values "$VALUES_FILE" \
        --timeout "$TIMEOUT" \
        --dry-run > /dev/null 2>&1; then
        log_success "Helm dry-run 验证通过"
    else
        log_error "Helm dry-run 验证失败"
        return 1
    fi
    
    # 执行实际部署
    log_info "执行实际 Helm 部署..."
    local deployment_start_time
    deployment_start_time=$(date +%s)
    
    if helm install "$RELEASE_NAME" "$CHART_PATH" \
        --namespace "$NAMESPACE" \
        --values "$VALUES_FILE" \
        --timeout "$TIMEOUT" \
        --wait; then
        
        local deployment_end_time
        deployment_end_time=$(date +%s)
        local deployment_duration=$((deployment_end_time - deployment_start_time))
        
        log_success "Helm 部署成功 (耗时: ${deployment_duration}秒)"
    else
        log_error "Helm 部署失败"
        return 1
    fi
}

# 验证 Kubernetes 资源状态
verify_kubernetes_resources() {
    log_info "验证 Kubernetes 资源状态..."
    
    # 检查 Helm release 状态
    local release_status
    release_status=$(helm status "$RELEASE_NAME" -n "$NAMESPACE" -o json | jq -r '.info.status' 2>/dev/null || echo "unknown")
    
    if [ "$release_status" = "deployed" ]; then
        log_success "Helm release 状态正常: $release_status"
    else
        log_error "Helm release 状态异常: $release_status"
    fi
    
    # 检查 Pod 状态
    log_info "检查 Pod 状态..."
    local total_pods
    total_pods=$(kubectl get pods -n "$NAMESPACE" --no-headers | wc -l || echo "0")
    
    if [ "$total_pods" -gt 0 ]; then
        log_info "发现 $total_pods 个 Pod"
        
        # 等待所有 Pod 就绪
        log_info "等待 Pod 就绪..."
        local max_wait=300  # 5分钟
        local wait_time=0
        
        while [ $wait_time -lt $max_wait ]; do
            local ready_pods
            ready_pods=$(kubectl get pods -n "$NAMESPACE" --no-headers | grep -c "Running\|Completed" || echo "0")
            
            if [ "$ready_pods" -eq "$total_pods" ]; then
                log_success "所有 Pod 已就绪 ($ready_pods/$total_pods)"
                break
            fi
            
            log_info "等待 Pod 就绪... ($ready_pods/$total_pods) - ${wait_time}s"
            sleep 10
            wait_time=$((wait_time + 10))
        done
        
        if [ $wait_time -ge $max_wait ]; then
            log_warning "部分 Pod 未在预期时间内就绪"
        fi
    else
        log_warning "未发现任何 Pod"
    fi
    
    # 检查 Service 状态
    log_info "检查 Service 状态..."
    local service_count
    service_count=$(kubectl get services -n "$NAMESPACE" --no-headers | wc -l || echo "0")
    
    if [ "$service_count" -gt 0 ]; then
        log_success "发现 $service_count 个 Service"
        
        # 列出所有服务
        kubectl get services -n "$NAMESPACE" --no-headers | while read -r line; do
            local service_name
            service_name=$(echo "$line" | awk '{print $1}')
            log_info "  - Service: $service_name"
        done
    else
        log_info "未发现 Service 资源"
    fi
    
    # 检查 Ingress 状态
    log_info "检查 Ingress 状态..."
    local ingress_count
    ingress_count=$(kubectl get ingress -n "$NAMESPACE" --no-headers 2>/dev/null | wc -l || echo "0")
    
    if [ "$ingress_count" -gt 0 ]; then
        log_success "发现 $ingress_count 个 Ingress"
        
        # 列出所有 Ingress
        kubectl get ingress -n "$NAMESPACE" --no-headers 2>/dev/null | while read -r line; do
            local ingress_name
            ingress_name=$(echo "$line" | awk '{print $1}')
            local ingress_hosts
            ingress_hosts=$(echo "$line" | awk '{print $3}')
            log_info "  - Ingress: $ingress_name (主机: $ingress_hosts)"
        done
    else
        log_info "未发现 Ingress 资源"
    fi
    
    # 检查 ConfigMap 状态
    log_info "检查 ConfigMap 状态..."
    local configmap_count
    configmap_count=$(kubectl get configmaps -n "$NAMESPACE" --no-headers | wc -l || echo "0")
    
    if [ "$configmap_count" -gt 0 ]; then
        log_success "发现 $configmap_count 个 ConfigMap"
    else
        log_info "未发现 ConfigMap 资源"
    fi
    
    # 检查 Secret 状态
    log_info "检查 Secret 状态..."
    local secret_count
    secret_count=$(kubectl get secrets -n "$NAMESPACE" --no-headers | wc -l || echo "0")
    
    if [ "$secret_count" -gt 0 ]; then
        log_success "发现 $secret_count 个 Secret"
    else
        log_info "未发现 Secret 资源"
    fi
}

# 监控部署过程中的事件
monitor_deployment_events() {
    log_info "监控部署事件..."
    
    # 获取命名空间中的事件
    local events
    events=$(kubectl get events -n "$NAMESPACE" --sort-by='.lastTimestamp' 2>/dev/null || echo "")
    
    if [ -n "$events" ]; then
        log_info "部署过程中的事件:"
        echo "$events" | tail -20 | while read -r line; do
            if echo "$line" | grep -q "Warning"; then
                log_warning "事件: $line"
            else
                log_info "事件: $line"
            fi
        done
    else
        log_info "未发现相关事件"
    fi
}

# 检查资源使用情况
check_resource_usage() {
    log_info "检查资源使用情况..."
    
    # 检查 Pod 资源使用（如果 metrics-server 可用）
    if kubectl top pods -n "$NAMESPACE" &> /dev/null; then
        log_info "Pod 资源使用情况:"
        kubectl top pods -n "$NAMESPACE" | while read -r line; do
            log_info "  $line"
        done
        log_success "资源使用情况检查完成"
    else
        log_info "metrics-server 不可用，跳过资源使用检查"
    fi
}

# 主函数
main() {
    log_info "开始 ESS-HELM 部署过程验证"
    log_info "测试时间: $(date)"
    log_info "命名空间: $NAMESPACE"
    log_info "Release 名称: $RELEASE_NAME"
    log_info "超时时间: $TIMEOUT"
    log_info "日志文件: $LOG_FILE"
    echo ""
    
    # 执行部署验证
    if ! prepare_test_environment; then
        log_error "测试环境准备失败"
        exit 1
    fi
    
    if ! execute_helm_deployment; then
        log_error "Helm 部署失败"
        exit 1
    fi
    
    verify_kubernetes_resources
    monitor_deployment_events
    check_resource_usage
    
    # 生成测试摘要
    echo ""
    log_info "=== 部署过程验证摘要 ==="
    log_info "总检查数: $TOTAL_CHECKS"
    log_info "通过检查: $PASSED_CHECKS"
    log_info "失败检查: $FAILED_CHECKS"
    log_info "警告数量: $WARNINGS"
    
    if [ "$FAILED_CHECKS" -eq 0 ]; then
        log_success "部署过程验证通过！"
        log_info "测试环境将在脚本结束时自动清理"
        exit 0
    else
        log_error "部分部署过程验证失败，请查看详细日志: $LOG_FILE"
        exit 1
    fi
}

# 执行主函数
main "$@"
