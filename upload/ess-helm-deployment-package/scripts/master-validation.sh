#!/bin/bash

# ESS-HELM 主验证脚本
# 执行完整的部署验证和服务稳定性测试流程
# 版本: 1.0
# 日期: 2025-01-19

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 全局变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$PROJECT_ROOT/master-validation-$(date +%Y%m%d-%H%M%S).log"
NAMESPACE="ess-test"

# 测试阶段
declare -a TEST_PHASES=(
    "部署前检查验证"
    "部署过程验证"
    "服务功能测试"
    "稳定性和性能评估"
    "生成详细报告"
)

# 测试脚本映射
declare -A TEST_SCRIPTS=(
    ["部署前检查验证"]="comprehensive-deployment-validation.sh"
    ["部署过程验证"]="deployment-process-validation.sh"
    ["服务功能测试"]="service-functionality-test.sh"
    ["稳定性和性能评估"]="stability-performance-assessment.sh"
    ["生成详细报告"]="generate-validation-report.sh"
)

# 测试结果
declare -A TEST_RESULTS=()
TOTAL_PHASES=${#TEST_PHASES[@]}
COMPLETED_PHASES=0
FAILED_PHASES=0

# 日志函数
log() {
    echo -e "$1" | tee -a "$LOG_FILE"
}

log_header() {
    log "${CYAN}========================================${NC}"
    log "${CYAN}$1${NC}"
    log "${CYAN}========================================${NC}"
}

log_info() {
    log "${BLUE}[INFO]${NC} $1"
}

log_success() {
    log "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    log "${RED}[ERROR]${NC} $1"
}

log_warning() {
    log "${YELLOW}[WARNING]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
ESS-HELM 主验证脚本

用法: $0 [选项]

选项:
  -h, --help              显示此帮助信息
  -v, --verbose           详细输出模式
  -s, --skip-cleanup      跳过测试环境清理
  -p, --phase PHASE       只执行指定阶段 (1-5)
  -n, --namespace NAME    指定测试命名空间 (默认: ess-test)
  --dry-run              只显示将要执行的测试，不实际运行

测试阶段:
  1. 部署前检查验证
  2. 部署过程验证
  3. 服务功能测试
  4. 稳定性和性能评估
  5. 生成详细报告

示例:
  $0                      # 执行完整验证流程
  $0 -p 1                 # 只执行部署前检查
  $0 -v                   # 详细输出模式
  $0 --dry-run            # 预览测试计划

EOF
}

# 解析命令行参数
parse_arguments() {
    VERBOSE=false
    SKIP_CLEANUP=false
    SPECIFIC_PHASE=""
    DRY_RUN=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -s|--skip-cleanup)
                SKIP_CLEANUP=true
                shift
                ;;
            -p|--phase)
                SPECIFIC_PHASE="$2"
                shift 2
                ;;
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 检查前提条件
check_prerequisites() {
    log_header "检查前提条件"
    
    # 检查必需工具
    local tools=("kubectl" "helm" "curl" "jq" "bc")
    local missing_tools=()
    
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        else
            log_success "$tool 已安装"
        fi
    done
    
    if [ ${#missing_tools[@]} -gt 0 ]; then
        log_error "缺少必需工具: ${missing_tools[*]}"
        log_info "请安装缺少的工具后重试"
        exit 1
    fi
    
    # 检查 Kubernetes 集群连接
    if kubectl cluster-info &> /dev/null; then
        log_success "Kubernetes 集群连接正常"
    else
        log_error "无法连接到 Kubernetes 集群"
        log_info "请检查 kubeconfig 配置"
        exit 1
    fi
    
    # 检查脚本文件存在性
    for phase in "${TEST_PHASES[@]}"; do
        local script_name="${TEST_SCRIPTS[$phase]}"
        local script_path="$SCRIPT_DIR/$script_name"
        
        if [ -f "$script_path" ]; then
            log_success "测试脚本存在: $script_name"
        else
            log_error "测试脚本不存在: $script_path"
            exit 1
        fi
    done
    
    log_success "前提条件检查通过"
}

# 显示测试计划
show_test_plan() {
    log_header "测试计划"
    
    log_info "测试环境配置:"
    log_info "  - 命名空间: $NAMESPACE"
    log_info "  - 日志文件: $LOG_FILE"
    log_info "  - 详细输出: $VERBOSE"
    log_info "  - 跳过清理: $SKIP_CLEANUP"
    
    if [ -n "$SPECIFIC_PHASE" ]; then
        log_info "  - 指定阶段: $SPECIFIC_PHASE"
    fi
    
    echo ""
    log_info "将要执行的测试阶段:"
    
    if [ -n "$SPECIFIC_PHASE" ]; then
        if [ "$SPECIFIC_PHASE" -ge 1 ] && [ "$SPECIFIC_PHASE" -le ${#TEST_PHASES[@]} ]; then
            local phase_index=$((SPECIFIC_PHASE - 1))
            local phase_name="${TEST_PHASES[$phase_index]}"
            local script_name="${TEST_SCRIPTS[$phase_name]}"
            log_info "  $SPECIFIC_PHASE. $phase_name ($script_name)"
        else
            log_error "无效的阶段编号: $SPECIFIC_PHASE"
            exit 1
        fi
    else
        for i in "${!TEST_PHASES[@]}"; do
            local phase_num=$((i + 1))
            local phase_name="${TEST_PHASES[$i]}"
            local script_name="${TEST_SCRIPTS[$phase_name]}"
            log_info "  $phase_num. $phase_name ($script_name)"
        done
    fi
    
    if $DRY_RUN; then
        log_info ""
        log_info "这是预览模式，不会实际执行测试"
        exit 0
    fi
}

# 执行单个测试阶段
execute_test_phase() {
    local phase_num="$1"
    local phase_name="$2"
    local script_name="$3"
    
    log_header "阶段 $phase_num: $phase_name"
    
    local script_path="$SCRIPT_DIR/$script_name"
    local phase_log="$PROJECT_ROOT/phase-$phase_num-$(date +%Y%m%d-%H%M%S).log"
    
    log_info "执行脚本: $script_name"
    log_info "阶段日志: $phase_log"
    
    local start_time
    start_time=$(date +%s)
    
    # 执行测试脚本
    local exit_code=0
    if $VERBOSE; then
        bash "$script_path" 2>&1 | tee "$phase_log" || exit_code=$?
    else
        bash "$script_path" > "$phase_log" 2>&1 || exit_code=$?
    fi
    
    local end_time
    end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # 记录结果
    if [ $exit_code -eq 0 ]; then
        TEST_RESULTS["$phase_name"]="PASS"
        log_success "阶段 $phase_num 完成 (耗时: ${duration}秒)"
        ((COMPLETED_PHASES++))
    else
        TEST_RESULTS["$phase_name"]="FAIL"
        log_error "阶段 $phase_num 失败 (耗时: ${duration}秒)"
        ((FAILED_PHASES++))
        
        if ! $VERBOSE; then
            log_info "错误详情请查看: $phase_log"
        fi
        
        # 询问是否继续
        if [ -n "$SPECIFIC_PHASE" ]; then
            return $exit_code
        fi
        
        echo ""
        read -p "是否继续执行后续阶段? (y/N): " -n 1 -r
        echo ""
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "用户选择停止测试"
            return $exit_code
        fi
    fi
    
    return 0
}

# 执行测试流程
execute_test_flow() {
    log_header "开始执行测试流程"
    
    if [ -n "$SPECIFIC_PHASE" ]; then
        # 执行指定阶段
        local phase_index=$((SPECIFIC_PHASE - 1))
        local phase_name="${TEST_PHASES[$phase_index]}"
        local script_name="${TEST_SCRIPTS[$phase_name]}"
        
        execute_test_phase "$SPECIFIC_PHASE" "$phase_name" "$script_name"
    else
        # 执行所有阶段
        for i in "${!TEST_PHASES[@]}"; do
            local phase_num=$((i + 1))
            local phase_name="${TEST_PHASES[$i]}"
            local script_name="${TEST_SCRIPTS[$phase_name]}"
            
            if ! execute_test_phase "$phase_num" "$phase_name" "$script_name"; then
                log_warning "阶段 $phase_num 失败，停止后续测试"
                break
            fi
            
            # 阶段间暂停
            if [ $phase_num -lt ${#TEST_PHASES[@]} ]; then
                log_info "等待 5 秒后继续下一阶段..."
                sleep 5
            fi
        done
    fi
}

# 生成测试摘要
generate_summary() {
    log_header "测试摘要"
    
    log_info "测试执行统计:"
    log_info "  - 总阶段数: $TOTAL_PHASES"
    log_info "  - 完成阶段: $COMPLETED_PHASES"
    log_info "  - 失败阶段: $FAILED_PHASES"
    
    echo ""
    log_info "各阶段结果:"
    
    for phase in "${TEST_PHASES[@]}"; do
        local result="${TEST_RESULTS[$phase]:-SKIP}"
        case $result in
            "PASS")
                log_success "  ✅ $phase"
                ;;
            "FAIL")
                log_error "  ❌ $phase"
                ;;
            "SKIP")
                log_warning "  ⏭️ $phase (跳过)"
                ;;
        esac
    done
    
    echo ""
    
    # 总体结论
    if [ $FAILED_PHASES -eq 0 ] && [ $COMPLETED_PHASES -gt 0 ]; then
        log_success "🎉 所有测试阶段通过！ESS-HELM 部署包验证成功"
        log_info "部署包已准备好用于生产环境"
    elif [ $COMPLETED_PHASES -gt 0 ]; then
        log_warning "⚠️ 部分测试阶段失败，需要进一步调查"
        log_info "建议修复发现的问题后重新测试"
    else
        log_error "❌ 测试执行失败，请检查环境配置"
    fi
    
    echo ""
    log_info "详细日志文件: $LOG_FILE"
    
    # 查找生成的报告文件
    local report_file
    report_file=$(find "$PROJECT_ROOT" -name "ESS-HELM-VALIDATION-REPORT-*.md" -type f -newer "$LOG_FILE" 2>/dev/null | head -n1 || echo "")
    
    if [ -n "$report_file" ]; then
        log_info "验证报告文件: $report_file"
    fi
}

# 清理测试环境
cleanup_test_environment() {
    if $SKIP_CLEANUP; then
        log_info "跳过测试环境清理"
        return 0
    fi
    
    log_header "清理测试环境"
    
    # 删除测试命名空间
    if kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_info "删除测试命名空间: $NAMESPACE"
        kubectl delete namespace "$NAMESPACE" --timeout=300s || true
        log_success "测试环境清理完成"
    else
        log_info "测试命名空间不存在，无需清理"
    fi
}

# 信号处理
cleanup_on_exit() {
    log_info ""
    log_info "收到退出信号，正在清理..."
    cleanup_test_environment
    exit 1
}

trap cleanup_on_exit INT TERM

# 主函数
main() {
    # 解析参数
    parse_arguments "$@"
    
    # 显示标题
    log_header "ESS-HELM 全面部署验证和服务稳定性测试"
    log_info "开始时间: $(date)"
    log_info "测试版本: ESS-HELM 25.6.2-dev"
    log_info "测试工具: 官方验证套件 v1.0"
    echo ""
    
    # 执行测试流程
    check_prerequisites
    show_test_plan
    execute_test_flow
    generate_summary
    
    # 清理环境
    if [ -z "$SPECIFIC_PHASE" ] || [ "$SPECIFIC_PHASE" -eq 5 ]; then
        cleanup_test_environment
    fi
    
    log_info ""
    log_info "测试完成时间: $(date)"
    
    # 返回适当的退出码
    if [ $FAILED_PHASES -eq 0 ] && [ $COMPLETED_PHASES -gt 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# 执行主函数
main "$@"
