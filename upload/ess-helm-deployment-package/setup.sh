#!/bin/bash

# ESS-HELM 一键部署主入口脚本
# 版本: v1.0
# 基于: ESS-HELM 25.6.2 官方稳定版
# 作者: ESS-HELM 部署团队
# 日期: 2025-06-20

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="ESS-HELM"
VERSION="25.6.2"
LOG_FILE="${SCRIPT_DIR}/deployment.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

info() {
    log "${BLUE}[信息]${NC} $1"
}

success() {
    log "${GREEN}[成功]${NC} $1"
}

warning() {
    log "${YELLOW}[警告]${NC} $1"
}

error() {
    log "${RED}[错误]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    clear
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    ESS-HELM 一键部署系统                      ║"
    echo "║                                                              ║"
    echo "║  版本: ${VERSION} (官方稳定版)                                ║"
    echo "║  功能: Router WAN IP检测 + 虚拟公网IP路由 + 增强管理           ║"
    echo "║  支持: 外部服务器 + 内部服务器 + 完整管理功能                  ║"
    echo "║                                                              ║"
    echo "║  🚀 一键部署 | 🛠️ 智能配置 | 🔧 增强管理                     ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo
}

# 环境检测函数
check_environment() {
    info "正在检测系统环境..."
    
    # 检测操作系统
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        info "检测到操作系统: Linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        info "检测到操作系统: macOS"
    else
        error "不支持的操作系统: $OSTYPE"
        exit 1
    fi
    
    # 检测必需工具
    local required_tools=("curl" "wget" "git" "docker" "kubectl" "helm")
    local missing_tools=()
    
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        else
            info "✓ $tool 已安装"
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        warning "以下工具需要安装: ${missing_tools[*]}"
        echo
        echo -e "${YELLOW}是否自动安装缺失的工具? (y/n):${NC}"
        read -r install_tools
        if [[ "$install_tools" =~ ^[Yy]$ ]]; then
            install_missing_tools "${missing_tools[@]}"
        else
            error "请手动安装缺失的工具后重新运行脚本"
            exit 1
        fi
    fi
    
    success "环境检测完成"
}

# 安装缺失工具
install_missing_tools() {
    local tools=("$@")
    info "正在安装缺失的工具..."
    
    for tool in "${tools[@]}"; do
        case "$tool" in
            "docker")
                install_docker
                ;;
            "kubectl")
                install_kubectl
                ;;
            "helm")
                install_helm
                ;;
            *)
                if [[ "$OS" == "linux" ]]; then
                    sudo apt-get update && sudo apt-get install -y "$tool"
                elif [[ "$OS" == "macos" ]]; then
                    brew install "$tool"
                fi
                ;;
        esac
    done
}

# 安装Docker
install_docker() {
    info "正在安装 Docker..."
    if [[ "$OS" == "linux" ]]; then
        curl -fsSL https://get.docker.com -o get-docker.sh
        sudo sh get-docker.sh
        sudo usermod -aG docker "$USER"
        rm get-docker.sh
    elif [[ "$OS" == "macos" ]]; then
        warning "请从 https://docs.docker.com/desktop/mac/install/ 下载并安装 Docker Desktop"
        exit 1
    fi
}

# 安装kubectl
install_kubectl() {
    info "正在安装 kubectl..."
    if [[ "$OS" == "linux" ]]; then
        curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
        sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
        rm kubectl
    elif [[ "$OS" == "macos" ]]; then
        curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/darwin/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
    fi
}

# 安装Helm
install_helm() {
    info "正在安装 Helm..."
    curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
}

# 配置收集函数
collect_configuration() {
    info "开始收集部署配置信息..."
    echo
    
    # 主域名配置
    echo -e "${CYAN}请输入您的主域名 (例如: example.com):${NC}"
    read -r MAIN_DOMAIN
    while [[ ! "$MAIN_DOMAIN" =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$ ]]; do
        echo -e "${RED}域名格式不正确，请重新输入:${NC}"
        read -r MAIN_DOMAIN
    done
    
    # 子域名配置
    echo -e "${CYAN}请配置子域名 (直接回车使用默认值):${NC}"
    echo -e "Element Web 子域名 [默认: element]:"
    read -r ELEMENT_SUBDOMAIN
    ELEMENT_SUBDOMAIN=${ELEMENT_SUBDOMAIN:-element}
    
    echo -e "Matrix 服务器子域名 [默认: matrix]:"
    read -r MATRIX_SUBDOMAIN
    MATRIX_SUBDOMAIN=${MATRIX_SUBDOMAIN:-matrix}
    
    echo -e "MAS 认证服务子域名 [默认: mas]:"
    read -r MAS_SUBDOMAIN
    MAS_SUBDOMAIN=${MAS_SUBDOMAIN:-mas}
    
    echo -e "RTC 服务子域名 [默认: rtc]:"
    read -r RTC_SUBDOMAIN
    RTC_SUBDOMAIN=${RTC_SUBDOMAIN:-rtc}
    
    echo -e "TURN 服务子域名 [默认: turn]:"
    read -r TURN_SUBDOMAIN
    TURN_SUBDOMAIN=${TURN_SUBDOMAIN:-turn}
    
    # 端口配置
    echo -e "${CYAN}请配置服务端口 (直接回车使用默认值):${NC}"
    echo -e "HTTPS 端口 [默认: 8443]:"
    read -r HTTPS_PORT
    HTTPS_PORT=${HTTPS_PORT:-8443}
    
    echo -e "Matrix 联邦端口 [默认: 8448]:"
    read -r MATRIX_FEDERATION_PORT
    MATRIX_FEDERATION_PORT=${MATRIX_FEDERATION_PORT:-8448}
    
    # 服务目录配置
    echo -e "${CYAN}请输入服务主目录路径 [默认: ~/matrix]:${NC}"
    read -r SERVICE_DIR
    SERVICE_DIR=${SERVICE_DIR:-~/matrix}
    SERVICE_DIR=$(eval echo "$SERVICE_DIR")  # 展开 ~ 符号
    
    # SSL证书配置
    echo -e "${CYAN}请选择SSL证书配置方式:${NC}"
    echo "1. Let's Encrypt 自动申请 (推荐)"
    echo "2. 使用自定义证书"
    echo "0. 返回主菜单"
    echo
    echo -e "${YELLOW}请选择 (1-2, 0返回):${NC}"
    read -r ssl_choice
    
    case "$ssl_choice" in
        1)
            SSL_MODE="letsencrypt"
            info "已选择 Let's Encrypt 自动申请证书"
            ;;
        2)
            SSL_MODE="custom"
            echo -e "${CYAN}请输入证书文件路径:${NC}"
            read -r SSL_CERT_PATH
            echo -e "${CYAN}请输入私钥文件路径:${NC}"
            read -r SSL_KEY_PATH
            ;;
        0)
            return 1
            ;;
        *)
            error "无效选择，请重新选择"
            collect_configuration
            return
            ;;
    esac
    
    # 显示配置摘要
    show_configuration_summary
}

# 显示配置摘要
show_configuration_summary() {
    echo
    echo -e "${GREEN}配置摘要:${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "主域名: ${WHITE}$MAIN_DOMAIN${NC}"
    echo -e "Element Web: ${WHITE}$ELEMENT_SUBDOMAIN.$MAIN_DOMAIN${NC}"
    echo -e "Matrix 服务器: ${WHITE}$MATRIX_SUBDOMAIN.$MAIN_DOMAIN${NC}"
    echo -e "MAS 认证服务: ${WHITE}$MAS_SUBDOMAIN.$MAIN_DOMAIN${NC}"
    echo -e "RTC 服务: ${WHITE}$RTC_SUBDOMAIN.$MAIN_DOMAIN${NC}"
    echo -e "TURN 服务: ${WHITE}$TURN_SUBDOMAIN.$MAIN_DOMAIN${NC}"
    echo -e "HTTPS 端口: ${WHITE}$HTTPS_PORT${NC}"
    echo -e "Matrix 联邦端口: ${WHITE}$MATRIX_FEDERATION_PORT${NC}"
    echo -e "服务目录: ${WHITE}$SERVICE_DIR${NC}"
    echo -e "SSL 模式: ${WHITE}$SSL_MODE${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo
    echo -e "${YELLOW}确认以上配置正确? (y/n):${NC}"
    read -r confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        collect_configuration
    fi
}

# 主菜单
show_main_menu() {
    while true; do
        clear
        show_welcome
        echo -e "${WHITE}请选择部署模式:${NC}"
        echo
        echo -e "${GREEN}1.${NC} 外部服务器部署 (公网访问，支持Router WAN IP检测)"
        echo -e "${GREEN}2.${NC} 内部服务器部署 (内网访问，简化配置)"
        echo -e "${GREEN}3.${NC} 管理现有部署 (启动/停止/重启/监控)"
        echo -e "${GREEN}4.${NC} 查看部署状态"
        echo -e "${GREEN}5.${NC} 查看帮助文档"
        echo -e "${GREEN}0.${NC} 退出程序"
        echo
        echo -e "${YELLOW}请选择 (0-5):${NC}"
        read -r choice
        
        case "$choice" in
            1)
                deploy_external_server
                ;;
            2)
                deploy_internal_server
                ;;
            3)
                manage_deployment
                ;;
            4)
                show_deployment_status
                ;;
            5)
                show_help
                ;;
            0)
                echo -e "${GREEN}感谢使用 ESS-HELM 一键部署系统！${NC}"
                exit 0
                ;;
            *)
                error "无效选择，请重新选择"
                sleep 2
                ;;
        esac
    done
}

# 外部服务器部署
deploy_external_server() {
    info "开始外部服务器部署..."
    if collect_configuration; then
        "${SCRIPT_DIR}/scripts/external.sh" \
            --domain "$MAIN_DOMAIN" \
            --element-subdomain "$ELEMENT_SUBDOMAIN" \
            --matrix-subdomain "$MATRIX_SUBDOMAIN" \
            --mas-subdomain "$MAS_SUBDOMAIN" \
            --rtc-subdomain "$RTC_SUBDOMAIN" \
            --turn-subdomain "$TURN_SUBDOMAIN" \
            --https-port "$HTTPS_PORT" \
            --federation-port "$MATRIX_FEDERATION_PORT" \
            --service-dir "$SERVICE_DIR" \
            --ssl-mode "$SSL_MODE" \
            ${SSL_CERT_PATH:+--ssl-cert "$SSL_CERT_PATH"} \
            ${SSL_KEY_PATH:+--ssl-key "$SSL_KEY_PATH"}
    fi
    
    echo -e "${YELLOW}按任意键返回主菜单...${NC}"
    read -r
}

# 内部服务器部署
deploy_internal_server() {
    info "开始内部服务器部署..."
    if collect_configuration; then
        "${SCRIPT_DIR}/scripts/internal.sh" \
            --domain "$MAIN_DOMAIN" \
            --element-subdomain "$ELEMENT_SUBDOMAIN" \
            --matrix-subdomain "$MATRIX_SUBDOMAIN" \
            --mas-subdomain "$MAS_SUBDOMAIN" \
            --rtc-subdomain "$RTC_SUBDOMAIN" \
            --turn-subdomain "$TURN_SUBDOMAIN" \
            --https-port "$HTTPS_PORT" \
            --federation-port "$MATRIX_FEDERATION_PORT" \
            --service-dir "$SERVICE_DIR" \
            --ssl-mode "$SSL_MODE" \
            ${SSL_CERT_PATH:+--ssl-cert "$SSL_CERT_PATH"} \
            ${SSL_KEY_PATH:+--ssl-key "$SSL_KEY_PATH"}
    fi
    
    echo -e "${YELLOW}按任意键返回主菜单...${NC}"
    read -r
}

# 管理现有部署
manage_deployment() {
    "${SCRIPT_DIR}/scripts/admin.sh"
}

# 显示部署状态
show_deployment_status() {
    info "正在检查部署状态..."
    "${SCRIPT_DIR}/scripts/admin.sh" --status
    
    echo -e "${YELLOW}按任意键返回主菜单...${NC}"
    read -r
}

# 显示帮助
show_help() {
    clear
    echo -e "${CYAN}ESS-HELM 一键部署系统帮助${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo
    echo -e "${WHITE}部署模式说明:${NC}"
    echo "• 外部服务器部署: 适用于需要公网访问的环境，支持Router WAN IP自动检测"
    echo "• 内部服务器部署: 适用于内网环境，配置相对简化"
    echo
    echo -e "${WHITE}核心功能:${NC}"
    echo "• Router WAN IP自动检测 (5秒检测间隔)"
    echo "• 虚拟公网IP路由高可用 (LiveKit: **********, TURN: **********)"
    echo "• 增强管理功能 (用户管理、服务控制、注册控制)"
    echo
    echo -e "${WHITE}技术规范:${NC}"
    echo "• 基于官方 ESS-HELM 25.6.2 稳定版"
    echo "• 支持 Kubernetes + Helm 部署"
    echo "• 完全本地化，无外部服务依赖"
    echo
    echo -e "${WHITE}更多文档:${NC}"
    echo "• 部署指南: docs/deployment-guide.md"
    echo "• 管理指南: docs/admin-guide.md"
    echo "• 故障排除: docs/troubleshooting.md"
    echo
    echo -e "${YELLOW}按任意键返回主菜单...${NC}"
    read -r
}

# 主程序入口
main() {
    # 创建日志文件
    touch "$LOG_FILE"
    
    # 检查是否以root权限运行
    if [[ $EUID -eq 0 ]]; then
        warning "不建议以root权限运行此脚本"
    fi
    
    # 环境检测
    check_environment
    
    # 显示主菜单
    show_main_menu
}

# 信号处理
trap 'error "脚本被中断"; exit 1' INT TERM

# 启动主程序
main "$@"
