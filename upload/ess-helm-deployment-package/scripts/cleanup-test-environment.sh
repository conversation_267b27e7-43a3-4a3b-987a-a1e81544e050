#!/bin/bash

# ESS-HELM 测试环境清理脚本
# 彻底清理部署验证测试环境，包括 Kubernetes 资源、本地文件和进程
# 版本: 1.0
# 日期: 2025-01-19

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 全局变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CLEANUP_LOG="$PROJECT_ROOT/cleanup-$(date +%Y%m%d-%H%M%S).log"
CLEANUP_REPORT="$PROJECT_ROOT/cleanup-report-$(date +%Y%m%d-%H%M%S).md"

# 默认测试命名空间列表
DEFAULT_TEST_NAMESPACES=("ess-test" "matrix-test" "ess-validation")

# 清理统计
TOTAL_OPERATIONS=0
SUCCESSFUL_OPERATIONS=0
FAILED_OPERATIONS=0
WARNINGS=0

# 日志函数
log() {
    echo -e "$1" | tee -a "$CLEANUP_LOG"
}

log_header() {
    log "${CYAN}========================================${NC}"
    log "${CYAN}$1${NC}"
    log "${CYAN}========================================${NC}"
}

log_info() {
    log "${BLUE}[INFO]${NC} $1"
}

log_success() {
    log "${GREEN}[SUCCESS]${NC} $1"
    ((SUCCESSFUL_OPERATIONS++))
}

log_error() {
    log "${RED}[ERROR]${NC} $1"
    ((FAILED_OPERATIONS++))
}

log_warning() {
    log "${YELLOW}[WARNING]${NC} $1"
    ((WARNINGS++))
}

# 操作函数
run_operation() {
    local operation_name="$1"
    local operation_command="$2"
    local ignore_errors="${3:-false}"
    
    ((TOTAL_OPERATIONS++))
    log_info "执行操作: $operation_name"
    
    if eval "$operation_command" >> "$CLEANUP_LOG" 2>&1; then
        log_success "$operation_name"
        return 0
    else
        if [ "$ignore_errors" = "true" ]; then
            log_warning "$operation_name (忽略错误)"
            ((WARNINGS++))
            ((SUCCESSFUL_OPERATIONS++))
            return 0
        else
            log_error "$operation_name"
            return 1
        fi
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
ESS-HELM 测试环境清理脚本

用法: $0 [选项]

选项:
  -h, --help              显示此帮助信息
  -v, --verbose           详细输出模式
  -f, --force             强制清理，不询问确认
  -n, --namespace NAME    指定要清理的命名空间 (可多次使用)
  -a, --all               清理所有 ESS 相关的命名空间
  --dry-run              只显示将要执行的清理操作，不实际执行
  --keep-logs            保留日志文件
  --keep-reports         保留报告文件

清理范围:
  1. Kubernetes 测试资源 (命名空间、Helm releases)
  2. 本地测试文件 (日志、报告、临时文件)
  3. 后台进程 (端口转发、测试进程)
  4. 系统状态重置

示例:
  $0                      # 交互式清理默认测试环境
  $0 -f                   # 强制清理，不询问确认
  $0 -n custom-test       # 清理指定命名空间
  $0 -a                   # 清理所有 ESS 相关命名空间
  $0 --dry-run            # 预览清理计划

EOF
}

# 解析命令行参数
parse_arguments() {
    VERBOSE=false
    FORCE=false
    DRY_RUN=false
    KEEP_LOGS=false
    KEEP_REPORTS=false
    CLEAN_ALL=false
    CUSTOM_NAMESPACES=()
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            -n|--namespace)
                CUSTOM_NAMESPACES+=("$2")
                shift 2
                ;;
            -a|--all)
                CLEAN_ALL=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --keep-logs)
                KEEP_LOGS=true
                shift
                ;;
            --keep-reports)
                KEEP_REPORTS=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 确认清理操作
confirm_cleanup() {
    if $FORCE || $DRY_RUN; then
        return 0
    fi
    
    log_warning "此操作将清理 ESS-HELM 测试环境中的所有资源和文件"
    log_warning "包括 Kubernetes 资源、本地文件和后台进程"
    echo ""
    read -p "确认继续清理操作? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "用户取消清理操作"
        exit 0
    fi
}

# 检查前提条件
check_prerequisites() {
    log_header "检查前提条件"
    
    # 检查必需工具
    local tools=("kubectl" "helm" "ps" "pkill")
    local missing_tools=()
    
    for tool in "${tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            log_success "$tool 已安装"
        else
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -gt 0 ]; then
        log_error "缺少必需工具: ${missing_tools[*]}"
        exit 1
    fi
    
    # 检查 Kubernetes 集群连接（可选）
    if kubectl cluster-info &> /dev/null; then
        log_success "Kubernetes 集群连接正常"
    else
        log_warning "无法连接到 Kubernetes 集群，将跳过 K8s 资源清理"
    fi
}

# 获取要清理的命名空间列表
get_namespaces_to_clean() {
    local namespaces=()
    
    if [ ${#CUSTOM_NAMESPACES[@]} -gt 0 ]; then
        namespaces=("${CUSTOM_NAMESPACES[@]}")
    elif $CLEAN_ALL; then
        # 查找所有 ESS 相关的命名空间
        if kubectl cluster-info &> /dev/null; then
            local found_namespaces
            found_namespaces=$(kubectl get namespaces -o name 2>/dev/null | \
                grep -E "(ess|matrix)" | \
                sed 's/namespace\///' || echo "")
            
            if [ -n "$found_namespaces" ]; then
                while IFS= read -r ns; do
                    namespaces+=("$ns")
                done <<< "$found_namespaces"
            fi
        fi
        
        # 添加默认命名空间
        for ns in "${DEFAULT_TEST_NAMESPACES[@]}"; do
            if [[ ! " ${namespaces[*]} " =~ " ${ns} " ]]; then
                namespaces+=("$ns")
            fi
        done
    else
        namespaces=("${DEFAULT_TEST_NAMESPACES[@]}")
    fi
    
    echo "${namespaces[@]}"
}

# 清理 Kubernetes 测试资源
cleanup_kubernetes_resources() {
    log_header "清理 Kubernetes 测试资源"
    
    # 检查集群连接
    if ! kubectl cluster-info &> /dev/null; then
        log_warning "无法连接到 Kubernetes 集群，跳过 K8s 资源清理"
        return 0
    fi
    
    # 获取要清理的命名空间
    local namespaces_to_clean
    read -ra namespaces_to_clean <<< "$(get_namespaces_to_clean)"
    
    log_info "将清理以下命名空间: ${namespaces_to_clean[*]}"
    
    for namespace in "${namespaces_to_clean[@]}"; do
        log_info "处理命名空间: $namespace"
        
        # 检查命名空间是否存在
        if ! kubectl get namespace "$namespace" &> /dev/null; then
            log_info "命名空间 $namespace 不存在，跳过"
            continue
        fi
        
        # 清理 Helm releases
        log_info "清理命名空间 $namespace 中的 Helm releases..."
        local helm_releases
        helm_releases=$(helm list -n "$namespace" -q 2>/dev/null || echo "")
        
        if [ -n "$helm_releases" ]; then
            echo "$helm_releases" | while read -r release; do
                if [ -n "$release" ]; then
                    if $DRY_RUN; then
                        log_info "[DRY-RUN] 将删除 Helm release: $release"
                    else
                        run_operation "删除 Helm release: $release" \
                            "helm uninstall '$release' -n '$namespace' --timeout 300s" true
                    fi
                fi
            done
        else
            log_info "命名空间 $namespace 中无 Helm releases"
        fi
        
        # 等待资源清理
        if ! $DRY_RUN; then
            log_info "等待资源清理完成..."
            sleep 10
        fi
        
        # 强制删除残留资源
        log_info "检查并清理残留资源..."
        local resource_types=("pods" "services" "ingress" "configmaps" "secrets" "persistentvolumeclaims")
        
        for resource_type in "${resource_types[@]}"; do
            local resources
            resources=$(kubectl get "$resource_type" -n "$namespace" --no-headers 2>/dev/null | awk '{print $1}' || echo "")
            
            if [ -n "$resources" ]; then
                echo "$resources" | while read -r resource; do
                    if [ -n "$resource" ]; then
                        if $DRY_RUN; then
                            log_info "[DRY-RUN] 将删除 $resource_type: $resource"
                        else
                            run_operation "强制删除 $resource_type: $resource" \
                                "kubectl delete '$resource_type' '$resource' -n '$namespace' --force --grace-period=0" true
                        fi
                    fi
                done
            fi
        done
        
        # 删除命名空间
        if $DRY_RUN; then
            log_info "[DRY-RUN] 将删除命名空间: $namespace"
        else
            run_operation "删除命名空间: $namespace" \
                "kubectl delete namespace '$namespace' --timeout=300s" true
        fi
    done
    
    log_success "Kubernetes 资源清理完成"
}

# 清理本地测试文件
cleanup_local_files() {
    log_header "清理本地测试文件"
    
    cd "$PROJECT_ROOT" || exit 1
    
    # 定义要清理的文件模式
    local file_patterns=(
        "deployment-validation-*.log"
        "deployment-process-*.log"
        "service-functionality-*.log"
        "stability-performance-*.log"
        "service-status-*.log"
        "master-validation-*.log"
        "phase-*-*.log"
    )
    
    if ! $KEEP_LOGS; then
        file_patterns+=(
            "*.log"
            "test-*.log"
            "validation-*.log"
        )
    fi
    
    if ! $KEEP_REPORTS; then
        file_patterns+=(
            "ESS-HELM-VALIDATION-REPORT-*.md"
            "deployment-validation-report-*.md"
            "cleanup-report-*.md"
        )
    fi
    
    # 清理匹配的文件
    for pattern in "${file_patterns[@]}"; do
        local files
        files=$(find . -maxdepth 1 -name "$pattern" -type f 2>/dev/null || echo "")
        
        if [ -n "$files" ]; then
            echo "$files" | while read -r file; do
                if [ -n "$file" ] && [ -f "$file" ]; then
                    if $DRY_RUN; then
                        log_info "[DRY-RUN] 将删除文件: $file"
                    else
                        run_operation "删除文件: $file" "rm -f '$file'" true
                    fi
                fi
            done
        fi
    done
    
    # 清理临时目录
    local temp_dirs=("tmp" "temp" ".tmp")
    for temp_dir in "${temp_dirs[@]}"; do
        if [ -d "$temp_dir" ]; then
            if $DRY_RUN; then
                log_info "[DRY-RUN] 将删除临时目录: $temp_dir"
            else
                run_operation "删除临时目录: $temp_dir" "rm -rf '$temp_dir'" true
            fi
        fi
    done
    
    log_success "本地文件清理完成"
}

# 清理后台进程
cleanup_background_processes() {
    log_header "清理后台进程"
    
    # 查找并终止 kubectl port-forward 进程
    log_info "查找 kubectl port-forward 进程..."
    local pf_pids
    pf_pids=$(ps aux | grep "kubectl.*port-forward" | grep -v grep | awk '{print $2}' || echo "")
    
    if [ -n "$pf_pids" ]; then
        echo "$pf_pids" | while read -r pid; do
            if [ -n "$pid" ]; then
                if $DRY_RUN; then
                    log_info "[DRY-RUN] 将终止端口转发进程: $pid"
                else
                    run_operation "终止端口转发进程: $pid" "kill '$pid'" true
                fi
            fi
        done
    else
        log_info "未发现 kubectl port-forward 进程"
    fi
    
    # 查找并终止测试相关进程
    log_info "查找测试相关进程..."
    local test_processes=("comprehensive-deployment-validation" "deployment-process-validation" 
                          "service-functionality-test" "stability-performance-assessment")
    
    for process_name in "${test_processes[@]}"; do
        local test_pids
        test_pids=$(ps aux | grep "$process_name" | grep -v grep | awk '{print $2}' || echo "")
        
        if [ -n "$test_pids" ]; then
            echo "$test_pids" | while read -r pid; do
                if [ -n "$pid" ]; then
                    if $DRY_RUN; then
                        log_info "[DRY-RUN] 将终止测试进程: $process_name ($pid)"
                    else
                        run_operation "终止测试进程: $process_name ($pid)" "kill '$pid'" true
                    fi
                fi
            done
        fi
    done
    
    # 清理可能的僵尸进程
    if ! $DRY_RUN; then
        run_operation "清理僵尸进程" "pkill -f 'ess.*test' || true" true
    fi
    
    log_success "后台进程清理完成"
}

# 验证清理结果
verify_cleanup_results() {
    log_header "验证清理结果"
    
    local verification_failed=false
    
    # 验证 Kubernetes 资源
    if kubectl cluster-info &> /dev/null; then
        local namespaces_to_check
        read -ra namespaces_to_check <<< "$(get_namespaces_to_clean)"
        
        for namespace in "${namespaces_to_check[@]}"; do
            if kubectl get namespace "$namespace" &> /dev/null; then
                log_warning "命名空间 $namespace 仍然存在"
                verification_failed=true
            else
                log_success "命名空间 $namespace 已成功删除"
            fi
        done
    fi
    
    # 验证本地文件
    local remaining_files
    remaining_files=$(find "$PROJECT_ROOT" -maxdepth 1 -name "*validation*.log" -o -name "*test*.log" 2>/dev/null | wc -l)
    
    if [ "$remaining_files" -eq 0 ] || $KEEP_LOGS; then
        log_success "本地测试文件清理完成"
    else
        log_warning "仍有 $remaining_files 个测试文件残留"
        verification_failed=true
    fi
    
    # 验证后台进程
    local remaining_processes
    remaining_processes=$(ps aux | grep -E "(kubectl.*port-forward|ess.*test)" | grep -v grep | wc -l || echo "0")
    
    if [ "$remaining_processes" -eq 0 ]; then
        log_success "后台进程清理完成"
    else
        log_warning "仍有 $remaining_processes 个相关进程运行"
        verification_failed=true
    fi
    
    if $verification_failed; then
        log_warning "清理验证发现一些问题，请手动检查"
        return 1
    else
        log_success "清理验证通过，环境已恢复干净状态"
        return 0
    fi
}

# 生成清理报告
generate_cleanup_report() {
    log_header "生成清理报告"
    
    if $DRY_RUN; then
        log_info "预览模式，跳过报告生成"
        return 0
    fi
    
    cat > "$CLEANUP_REPORT" << EOF
# ESS-HELM 测试环境清理报告

## 📋 清理概览

**清理时间**: $(date '+%Y-%m-%d %H:%M:%S')  
**清理版本**: ESS-HELM 清理工具 v1.0  
**执行模式**: $([ "$FORCE" = "true" ] && echo "强制模式" || echo "交互模式")  

---

## 📊 清理统计

- **总操作数**: $TOTAL_OPERATIONS
- **成功操作**: $SUCCESSFUL_OPERATIONS  
- **失败操作**: $FAILED_OPERATIONS
- **警告数量**: $WARNINGS

---

## 🧹 清理范围

### Kubernetes 资源清理
$(if kubectl cluster-info &> /dev/null; then
    echo "✅ **已执行** - 清理测试命名空间和相关资源"
    local namespaces_cleaned
    read -ra namespaces_cleaned <<< "$(get_namespaces_to_clean)"
    for ns in "${namespaces_cleaned[@]}"; do
        echo "- 命名空间: $ns"
    done
else
    echo "⏭️ **跳过** - 无法连接到 Kubernetes 集群"
fi)

### 本地文件清理
✅ **已执行** - 清理测试日志和临时文件
- 日志文件: $([ "$KEEP_LOGS" = "true" ] && echo "保留" || echo "已清理")
- 报告文件: $([ "$KEEP_REPORTS" = "true" ] && echo "保留" || echo "已清理")
- 临时文件: 已清理

### 后台进程清理
✅ **已执行** - 终止测试相关进程
- kubectl port-forward 进程
- 测试验证脚本进程
- 其他相关后台进程

---

## 🔍 清理结果验证

$(if verify_cleanup_results &> /dev/null; then
    echo "✅ **验证通过** - 环境已恢复到干净状态"
else
    echo "⚠️ **需要关注** - 发现一些残留项目，请手动检查"
fi)

---

## 📝 详细日志

详细的清理操作日志请查看: \`$CLEANUP_LOG\`

---

## 🎯 下次测试准备

环境已准备就绪，可以进行下次 ESS-HELM 部署验证测试：

\`\`\`bash
# 执行完整验证测试
./scripts/master-validation.sh

# 或执行单个阶段测试
./scripts/master-validation.sh -p 1
\`\`\`

---

**报告生成时间**: $(date '+%Y-%m-%d %H:%M:%S')  
**清理工具版本**: v1.0
EOF
    
    log_success "清理报告已生成: $CLEANUP_REPORT"
}

# 主函数
main() {
    # 解析参数
    parse_arguments "$@"
    
    # 显示标题
    log_header "ESS-HELM 测试环境清理工具"
    log_info "开始时间: $(date)"
    log_info "清理日志: $CLEANUP_LOG"
    log_info "清理报告: $CLEANUP_REPORT"
    
    if $DRY_RUN; then
        log_info "运行模式: 预览模式 (不会实际执行清理)"
    fi
    
    echo ""
    
    # 执行清理流程
    check_prerequisites
    confirm_cleanup
    cleanup_kubernetes_resources
    cleanup_local_files
    cleanup_background_processes
    verify_cleanup_results
    generate_cleanup_report
    
    # 生成清理摘要
    echo ""
    log_header "清理摘要"
    log_info "总操作数: $TOTAL_OPERATIONS"
    log_info "成功操作: $SUCCESSFUL_OPERATIONS"
    log_info "失败操作: $FAILED_OPERATIONS"
    log_info "警告数量: $WARNINGS"
    
    if [ $FAILED_OPERATIONS -eq 0 ]; then
        log_success "🎉 测试环境清理完成！"
        log_info "环境已恢复到干净状态，可以进行下次测试"
    else
        log_warning "⚠️ 清理过程中发现一些问题"
        log_info "请查看详细日志: $CLEANUP_LOG"
    fi
    
    echo ""
    log_info "清理完成时间: $(date)"
    
    # 返回适当的退出码
    if [ $FAILED_OPERATIONS -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# 执行主函数
main "$@"
