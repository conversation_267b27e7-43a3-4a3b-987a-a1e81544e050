#!/bin/bash

# ESS-HELM 快速清理脚本
# 快速清理测试环境，适用于日常开发和测试
# 版本: 1.0
# 日期: 2025-01-19

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 全局变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
ESS-HELM 快速清理脚本

用法: $0 [选项]

选项:
  -h, --help              显示此帮助信息
  -f, --force             强制清理，不询问确认
  -k, --keep-logs         保留日志文件
  --dry-run              只显示将要执行的操作

快速清理包括:
  1. 删除测试命名空间 ess-test
  2. 清理测试日志文件
  3. 终止端口转发进程
  4. 清理临时文件

示例:
  $0                      # 交互式快速清理
  $0 -f                   # 强制快速清理
  $0 --dry-run            # 预览清理操作

EOF
}

# 解析命令行参数
parse_arguments() {
    FORCE=false
    DRY_RUN=false
    KEEP_LOGS=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            -k|--keep-logs)
                KEEP_LOGS=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 确认清理操作
confirm_cleanup() {
    if $FORCE || $DRY_RUN; then
        return 0
    fi
    
    echo ""
    log_warning "快速清理将执行以下操作:"
    log_info "  - 删除测试命名空间 ess-test"
    log_info "  - 清理测试日志文件"
    log_info "  - 终止端口转发进程"
    log_info "  - 清理临时文件"
    echo ""
    read -p "确认继续? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "用户取消清理操作"
        exit 0
    fi
}

# 快速清理 Kubernetes 资源
quick_cleanup_k8s() {
    log_info "清理 Kubernetes 测试资源..."
    
    # 检查集群连接
    if ! kubectl cluster-info &> /dev/null; then
        log_warning "无法连接到 Kubernetes 集群，跳过 K8s 资源清理"
        return 0
    fi
    
    # 清理测试命名空间
    local namespace="ess-test"
    
    if kubectl get namespace "$namespace" &> /dev/null; then
        if $DRY_RUN; then
            log_info "[DRY-RUN] 将删除命名空间: $namespace"
        else
            log_info "删除命名空间: $namespace"
            
            # 先清理 Helm releases
            local helm_releases
            helm_releases=$(helm list -n "$namespace" -q 2>/dev/null || echo "")
            
            if [ -n "$helm_releases" ]; then
                echo "$helm_releases" | while read -r release; do
                    if [ -n "$release" ]; then
                        log_info "删除 Helm release: $release"
                        helm uninstall "$release" -n "$namespace" --timeout 60s &> /dev/null || true
                    fi
                done
            fi
            
            # 删除命名空间
            kubectl delete namespace "$namespace" --timeout=120s &> /dev/null || true
            log_success "命名空间 $namespace 已删除"
        fi
    else
        log_info "命名空间 $namespace 不存在，跳过"
    fi
}

# 快速清理本地文件
quick_cleanup_files() {
    log_info "清理本地测试文件..."
    
    cd "$PROJECT_ROOT" || exit 1
    
    local files_cleaned=0
    
    if ! $KEEP_LOGS; then
        # 清理日志文件
        local log_patterns=("*validation*.log" "*test*.log" "master-validation*.log" "phase-*.log")
        
        for pattern in "${log_patterns[@]}"; do
            local files
            files=$(find . -maxdepth 1 -name "$pattern" -type f 2>/dev/null || echo "")
            
            if [ -n "$files" ]; then
                echo "$files" | while read -r file; do
                    if [ -n "$file" ] && [ -f "$file" ]; then
                        if $DRY_RUN; then
                            log_info "[DRY-RUN] 将删除: $file"
                        else
                            rm -f "$file"
                            ((files_cleaned++))
                        fi
                    fi
                done
            fi
        done
    fi
    
    # 清理临时报告文件
    local temp_reports
    temp_reports=$(find . -maxdepth 1 -name "cleanup-report-*.md" -type f 2>/dev/null || echo "")
    
    if [ -n "$temp_reports" ]; then
        echo "$temp_reports" | while read -r file; do
            if [ -n "$file" ] && [ -f "$file" ]; then
                if $DRY_RUN; then
                    log_info "[DRY-RUN] 将删除: $file"
                else
                    rm -f "$file"
                    ((files_cleaned++))
                fi
            fi
        done
    fi
    
    if ! $DRY_RUN; then
        if [ $files_cleaned -gt 0 ]; then
            log_success "已清理 $files_cleaned 个文件"
        else
            log_info "未发现需要清理的文件"
        fi
    fi
}

# 快速清理后台进程
quick_cleanup_processes() {
    log_info "清理后台进程..."
    
    local processes_killed=0
    
    # 终止 kubectl port-forward 进程
    local pf_pids
    pf_pids=$(ps aux | grep "kubectl.*port-forward" | grep -v grep | awk '{print $2}' || echo "")
    
    if [ -n "$pf_pids" ]; then
        echo "$pf_pids" | while read -r pid; do
            if [ -n "$pid" ]; then
                if $DRY_RUN; then
                    log_info "[DRY-RUN] 将终止端口转发进程: $pid"
                else
                    kill "$pid" &> /dev/null || true
                    ((processes_killed++))
                fi
            fi
        done
    fi
    
    # 终止测试脚本进程
    local test_pids
    test_pids=$(ps aux | grep -E "(validation|test)\.sh" | grep -v grep | grep -v "quick-cleanup" | awk '{print $2}' || echo "")
    
    if [ -n "$test_pids" ]; then
        echo "$test_pids" | while read -r pid; do
            if [ -n "$pid" ]; then
                if $DRY_RUN; then
                    log_info "[DRY-RUN] 将终止测试进程: $pid"
                else
                    kill "$pid" &> /dev/null || true
                    ((processes_killed++))
                fi
            fi
        done
    fi
    
    if ! $DRY_RUN; then
        if [ $processes_killed -gt 0 ]; then
            log_success "已终止 $processes_killed 个进程"
        else
            log_info "未发现需要终止的进程"
        fi
    fi
}

# 验证清理结果
verify_quick_cleanup() {
    log_info "验证清理结果..."
    
    local issues=0
    
    # 检查命名空间
    if kubectl cluster-info &> /dev/null; then
        if kubectl get namespace "ess-test" &> /dev/null; then
            log_warning "命名空间 ess-test 仍然存在"
            ((issues++))
        else
            log_success "测试命名空间已清理"
        fi
    fi
    
    # 检查文件
    local remaining_logs
    remaining_logs=$(find "$PROJECT_ROOT" -maxdepth 1 -name "*validation*.log" -o -name "*test*.log" 2>/dev/null | wc -l | tr -d ' ')

    if [ "$remaining_logs" -eq 0 ] || $KEEP_LOGS; then
        log_success "测试文件已清理"
    else
        log_warning "仍有 $remaining_logs 个日志文件"
        ((issues++))
    fi

    # 检查进程
    local remaining_processes
    remaining_processes=$(ps aux | grep -E "(kubectl.*port-forward|validation.*test)" | grep -v grep | wc -l | tr -d ' ')
    
    if [ "$remaining_processes" -eq 0 ]; then
        log_success "后台进程已清理"
    else
        log_warning "仍有 $remaining_processes 个相关进程"
        ((issues++))
    fi
    
    if [ $issues -eq 0 ]; then
        log_success "✅ 快速清理完成，环境已就绪"
    else
        log_warning "⚠️ 发现 $issues 个问题，可能需要手动处理"
    fi
    
    return $issues
}

# 主函数
main() {
    # 解析参数
    parse_arguments "$@"
    
    # 显示标题
    echo ""
    log_info "🧹 ESS-HELM 快速清理工具"
    log_info "开始时间: $(date '+%H:%M:%S')"
    
    if $DRY_RUN; then
        log_info "运行模式: 预览模式"
    fi
    
    # 执行清理
    confirm_cleanup
    
    echo ""
    quick_cleanup_k8s
    quick_cleanup_files
    quick_cleanup_processes
    
    echo ""
    verify_quick_cleanup
    
    echo ""
    log_info "完成时间: $(date '+%H:%M:%S')"
    
    if ! $DRY_RUN; then
        echo ""
        log_info "💡 提示:"
        log_info "  - 如需完整清理，请使用: ./scripts/cleanup-test-environment.sh"
        log_info "  - 如需开始新测试，请使用: ./scripts/master-validation.sh"
    fi
    
    echo ""
}

# 执行主函数
main "$@"
