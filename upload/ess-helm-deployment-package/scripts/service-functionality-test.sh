#!/bin/bash

# ESS-HELM 服务功能测试脚本
# 验证所有服务端点的可访问性和响应性，测试核心业务功能
# 版本: 1.0
# 日期: 2025-01-19

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 全局变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
NAMESPACE="ess-test"
RELEASE_NAME="ess-test"
LOG_FILE="$PROJECT_ROOT/service-functionality-$(date +%Y%m%d-%H%M%S).log"
TIMEOUT=30

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
WARNINGS=0

# 服务端点配置
declare -A SERVICE_ENDPOINTS=(
    ["synapse"]="/_matrix/client/versions"
    ["element-web"]="/"
    ["matrix-authentication-service"]="/health"
    ["matrix-rtc"]="/health"
    ["postgres"]=""  # 数据库不直接暴露HTTP端点
)

# 日志函数
log() {
    echo -e "$1" | tee -a "$LOG_FILE"
}

log_info() {
    log "${BLUE}[INFO]${NC} $1"
}

log_success() {
    log "${GREEN}[PASS]${NC} $1"
    ((PASSED_TESTS++))
}

log_error() {
    log "${RED}[FAIL]${NC} $1"
    ((FAILED_TESTS++))
}

log_warning() {
    log "${YELLOW}[WARN]${NC} $1"
    ((WARNINGS++))
}

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    ((TOTAL_TESTS++))
    log_info "执行测试: $test_name"
    
    if eval "$test_command" >> "$LOG_FILE" 2>&1; then
        log_success "$test_name"
        return 0
    else
        log_error "$test_name"
        return 1
    fi
}

# 检查部署状态
check_deployment_status() {
    log_info "=== 3. 服务功能测试 ==="
    log_info "检查部署状态..."
    
    # 检查命名空间是否存在
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_error "测试命名空间 $NAMESPACE 不存在，请先运行部署过程验证"
        exit 1
    fi
    
    # 检查 Helm release 状态
    if ! helm list -n "$NAMESPACE" | grep -q "$RELEASE_NAME"; then
        log_error "Helm release $RELEASE_NAME 不存在，请先运行部署过程验证"
        exit 1
    fi
    
    local release_status
    release_status=$(helm status "$RELEASE_NAME" -n "$NAMESPACE" -o json | jq -r '.info.status' 2>/dev/null || echo "unknown")
    
    if [ "$release_status" = "deployed" ]; then
        log_success "Helm release 状态正常"
    else
        log_error "Helm release 状态异常: $release_status"
        exit 1
    fi
}

# 测试 Pod 健康状态
test_pod_health() {
    log_info "测试 Pod 健康状态..."
    
    local pods
    pods=$(kubectl get pods -n "$NAMESPACE" --no-headers 2>/dev/null || echo "")
    
    if [ -z "$pods" ]; then
        log_error "未发现任何 Pod"
        return 1
    fi
    
    echo "$pods" | while read -r line; do
        local pod_name
        local pod_status
        pod_name=$(echo "$line" | awk '{print $1}')
        pod_status=$(echo "$line" | awk '{print $3}')
        
        if [ "$pod_status" = "Running" ] || [ "$pod_status" = "Completed" ]; then
            log_success "Pod $pod_name 状态正常: $pod_status"
        else
            log_error "Pod $pod_name 状态异常: $pod_status"
        fi
    done
}

# 测试服务端点可访问性
test_service_endpoints() {
    log_info "测试服务端点可访问性..."
    
    # 获取所有服务
    local services
    services=$(kubectl get services -n "$NAMESPACE" --no-headers 2>/dev/null || echo "")
    
    if [ -z "$services" ]; then
        log_warning "未发现任何 Service"
        return 0
    fi
    
    echo "$services" | while read -r line; do
        local service_name
        local service_type
        local cluster_ip
        local ports
        
        service_name=$(echo "$line" | awk '{print $1}')
        service_type=$(echo "$line" | awk '{print $2}')
        cluster_ip=$(echo "$line" | awk '{print $3}')
        ports=$(echo "$line" | awk '{print $5}')
        
        log_info "测试服务: $service_name (类型: $service_type, IP: $cluster_ip)"
        
        # 提取端口号
        local port
        port=$(echo "$ports" | grep -o '[0-9]*' | head -n1)
        
        if [ -n "$port" ] && [ "$cluster_ip" != "None" ] && [ "$cluster_ip" != "<none>" ]; then
            # 使用 kubectl port-forward 测试连接
            local test_result=0
            
            # 启动端口转发（后台运行）
            kubectl port-forward -n "$NAMESPACE" "service/$service_name" "8080:$port" &
            local pf_pid=$!
            
            # 等待端口转发建立
            sleep 3
            
            # 测试连接
            if curl -s --connect-timeout 5 "http://localhost:8080" > /dev/null 2>&1; then
                log_success "服务 $service_name 端点可访问"
            else
                log_warning "服务 $service_name 端点连接测试失败（可能需要特定路径）"
            fi
            
            # 清理端口转发
            kill $pf_pid 2>/dev/null || true
            sleep 1
        else
            log_info "服务 $service_name 跳过端点测试（无有效端口或IP）"
        fi
    done
}

# 测试 Matrix 服务器功能
test_matrix_server() {
    log_info "测试 Matrix 服务器功能..."
    
    # 查找 Synapse 服务
    local synapse_service
    synapse_service=$(kubectl get services -n "$NAMESPACE" --no-headers | grep -i synapse | head -n1 || echo "")
    
    if [ -z "$synapse_service" ]; then
        log_warning "未找到 Synapse 服务"
        return 0
    fi
    
    local service_name
    local port
    service_name=$(echo "$synapse_service" | awk '{print $1}')
    port=$(echo "$synapse_service" | awk '{print $5}' | grep -o '[0-9]*' | head -n1)
    
    if [ -n "$port" ]; then
        log_info "测试 Matrix 服务器 API..."
        
        # 启动端口转发
        kubectl port-forward -n "$NAMESPACE" "service/$service_name" "8080:$port" &
        local pf_pid=$!
        sleep 3
        
        # 测试 Matrix 客户端 API 版本端点
        if curl -s --connect-timeout 10 "http://localhost:8080/_matrix/client/versions" | jq . > /dev/null 2>&1; then
            log_success "Matrix 客户端 API 响应正常"
        else
            log_warning "Matrix 客户端 API 测试失败"
        fi
        
        # 测试服务器信息端点
        if curl -s --connect-timeout 10 "http://localhost:8080/_matrix/federation/v1/version" > /dev/null 2>&1; then
            log_success "Matrix 联邦 API 响应正常"
        else
            log_warning "Matrix 联邦 API 测试失败"
        fi
        
        # 清理端口转发
        kill $pf_pid 2>/dev/null || true
        sleep 1
    fi
}

# 测试数据库连接
test_database_connection() {
    log_info "测试数据库连接..."
    
    # 查找 PostgreSQL Pod
    local postgres_pod
    postgres_pod=$(kubectl get pods -n "$NAMESPACE" --no-headers | grep -i postgres | head -n1 | awk '{print $1}' || echo "")
    
    if [ -z "$postgres_pod" ]; then
        log_warning "未找到 PostgreSQL Pod"
        return 0
    fi
    
    # 测试数据库连接
    if kubectl exec -n "$NAMESPACE" "$postgres_pod" -- pg_isready > /dev/null 2>&1; then
        log_success "PostgreSQL 数据库连接正常"
    else
        log_warning "PostgreSQL 数据库连接测试失败"
    fi
    
    # 测试数据库查询
    if kubectl exec -n "$NAMESPACE" "$postgres_pod" -- psql -U postgres -c "SELECT version();" > /dev/null 2>&1; then
        log_success "PostgreSQL 数据库查询正常"
    else
        log_warning "PostgreSQL 数据库查询测试失败"
    fi
}

# 测试配置参数应用
test_configuration_application() {
    log_info "测试配置参数应用..."
    
    # 检查 ConfigMap 内容
    local configmaps
    configmaps=$(kubectl get configmaps -n "$NAMESPACE" --no-headers | awk '{print $1}' || echo "")
    
    if [ -n "$configmaps" ]; then
        echo "$configmaps" | while read -r cm_name; do
            if [ -n "$cm_name" ]; then
                log_info "检查 ConfigMap: $cm_name"
                
                # 获取 ConfigMap 内容并检查是否包含预期配置
                local cm_content
                cm_content=$(kubectl get configmap "$cm_name" -n "$NAMESPACE" -o yaml 2>/dev/null || echo "")
                
                if [ -n "$cm_content" ]; then
                    log_success "ConfigMap $cm_name 内容正常"
                else
                    log_warning "ConfigMap $cm_name 内容获取失败"
                fi
            fi
        done
    else
        log_info "未发现 ConfigMap 资源"
    fi
}

# 测试服务间通信
test_service_communication() {
    log_info "测试服务间通信..."
    
    # 获取所有运行中的 Pod
    local running_pods
    running_pods=$(kubectl get pods -n "$NAMESPACE" --no-headers | grep "Running" | awk '{print $1}' || echo "")
    
    if [ -z "$running_pods" ]; then
        log_warning "未找到运行中的 Pod"
        return 0
    fi
    
    # 从第一个 Pod 测试到其他服务的连接
    local test_pod
    test_pod=$(echo "$running_pods" | head -n1)
    
    if [ -n "$test_pod" ]; then
        log_info "使用 Pod $test_pod 测试服务间通信..."
        
        # 获取所有服务
        local services
        services=$(kubectl get services -n "$NAMESPACE" --no-headers | awk '{print $1}' || echo "")
        
        echo "$services" | while read -r service_name; do
            if [ -n "$service_name" ]; then
                # 测试 DNS 解析
                if kubectl exec -n "$NAMESPACE" "$test_pod" -- nslookup "$service_name" > /dev/null 2>&1; then
                    log_success "服务 $service_name DNS 解析正常"
                else
                    log_warning "服务 $service_name DNS 解析失败"
                fi
            fi
        done
    fi
}

# 检查日志输出
check_service_logs() {
    log_info "检查服务日志输出..."
    
    # 获取所有 Pod
    local pods
    pods=$(kubectl get pods -n "$NAMESPACE" --no-headers | awk '{print $1}' || echo "")
    
    echo "$pods" | while read -r pod_name; do
        if [ -n "$pod_name" ]; then
            log_info "检查 Pod $pod_name 日志..."
            
            # 获取最近的日志
            local log_lines
            log_lines=$(kubectl logs "$pod_name" -n "$NAMESPACE" --tail=10 2>/dev/null | wc -l || echo "0")
            
            if [ "$log_lines" -gt 0 ]; then
                log_success "Pod $pod_name 日志输出正常"
                
                # 检查是否有错误日志
                local error_count
                error_count=$(kubectl logs "$pod_name" -n "$NAMESPACE" --tail=50 2>/dev/null | grep -i -c "error\|exception\|failed" || echo "0")
                
                if [ "$error_count" -gt 0 ]; then
                    log_warning "Pod $pod_name 发现 $error_count 条错误日志"
                else
                    log_success "Pod $pod_name 无明显错误日志"
                fi
            else
                log_warning "Pod $pod_name 无日志输出"
            fi
        fi
    done
}

# 主函数
main() {
    log_info "开始 ESS-HELM 服务功能测试"
    log_info "测试时间: $(date)"
    log_info "命名空间: $NAMESPACE"
    log_info "日志文件: $LOG_FILE"
    echo ""
    
    # 执行功能测试
    check_deployment_status
    test_pod_health
    test_service_endpoints
    test_matrix_server
    test_database_connection
    test_configuration_application
    test_service_communication
    check_service_logs
    
    # 生成测试摘要
    echo ""
    log_info "=== 服务功能测试摘要 ==="
    log_info "总测试数: $TOTAL_TESTS"
    log_info "通过测试: $PASSED_TESTS"
    log_info "失败测试: $FAILED_TESTS"
    log_info "警告数量: $WARNINGS"
    
    if [ "$FAILED_TESTS" -eq 0 ]; then
        log_success "服务功能测试通过！"
        exit 0
    else
        log_error "部分服务功能测试失败，请查看详细日志: $LOG_FILE"
        exit 1
    fi
}

# 执行主函数
main "$@"
