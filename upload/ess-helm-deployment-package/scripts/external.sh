#!/bin/bash

# ESS-HELM 外部服务器部署脚本
# 版本: v1.0
# 基于: ESS-HELM 25.6.2 官方稳定版
# 功能: 支持Router WAN IP检测 + 虚拟公网IP路由高可用
# 作者: ESS-HELM 部署团队
# 日期: 2025-06-20

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
ESS_HELM_VERSION="25.6.2"
NAMESPACE="matrix"
RELEASE_NAME="matrix-stack"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m'

# 默认配置
DEFAULT_HTTPS_PORT="8443"
DEFAULT_FEDERATION_PORT="8448"
DEFAULT_SERVICE_DIR="$HOME/matrix"
DEFAULT_SSL_MODE="letsencrypt"

# 日志函数
log() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

info() {
    log "${BLUE}[信息]${NC} $1"
}

success() {
    log "${GREEN}[成功]${NC} $1"
}

warning() {
    log "${YELLOW}[警告]${NC} $1"
}

error() {
    log "${RED}[错误]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "ESS-HELM 外部服务器部署脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "必需参数:"
    echo "  --domain DOMAIN              主域名 (例如: example.com)"
    echo
    echo "可选参数:"
    echo "  --element-subdomain SUB      Element Web子域名 (默认: element)"
    echo "  --matrix-subdomain SUB       Matrix服务器子域名 (默认: matrix)"
    echo "  --mas-subdomain SUB          MAS认证服务子域名 (默认: mas)"
    echo "  --rtc-subdomain SUB          RTC服务子域名 (默认: rtc)"
    echo "  --turn-subdomain SUB         TURN服务子域名 (默认: turn)"
    echo "  --https-port PORT            HTTPS端口 (默认: 8443)"
    echo "  --federation-port PORT       Matrix联邦端口 (默认: 8448)"
    echo "  --service-dir DIR            服务目录 (默认: ~/matrix)"
    echo "  --ssl-mode MODE              SSL模式 (letsencrypt|custom)"
    echo "  --ssl-cert PATH              自定义SSL证书路径"
    echo "  --ssl-key PATH               自定义SSL私钥路径"
    echo "  --router-ip IP               RouterOS IP地址"
    echo "  --router-username USER       RouterOS用户名"
    echo "  --router-password PASS       RouterOS密码"
    echo "  --wan-interface IFACE        WAN接口名称 (默认: ether1)"
    echo "  -h, --help                   显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 --domain example.com"
    echo "  $0 --domain example.com --router-ip *********** --router-username admin"
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --domain)
                MAIN_DOMAIN="$2"
                shift 2
                ;;
            --element-subdomain)
                ELEMENT_SUBDOMAIN="$2"
                shift 2
                ;;
            --matrix-subdomain)
                MATRIX_SUBDOMAIN="$2"
                shift 2
                ;;
            --mas-subdomain)
                MAS_SUBDOMAIN="$2"
                shift 2
                ;;
            --rtc-subdomain)
                RTC_SUBDOMAIN="$2"
                shift 2
                ;;
            --turn-subdomain)
                TURN_SUBDOMAIN="$2"
                shift 2
                ;;
            --https-port)
                HTTPS_PORT="$2"
                shift 2
                ;;
            --federation-port)
                FEDERATION_PORT="$2"
                shift 2
                ;;
            --service-dir)
                SERVICE_DIR="$2"
                shift 2
                ;;
            --ssl-mode)
                SSL_MODE="$2"
                shift 2
                ;;
            --ssl-cert)
                SSL_CERT_PATH="$2"
                shift 2
                ;;
            --ssl-key)
                SSL_KEY_PATH="$2"
                shift 2
                ;;
            --router-ip)
                ROUTER_IP="$2"
                shift 2
                ;;
            --router-username)
                ROUTER_USERNAME="$2"
                shift 2
                ;;
            --router-password)
                ROUTER_PASSWORD="$2"
                shift 2
                ;;
            --wan-interface)
                WAN_INTERFACE="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查必需参数
    if [[ -z "${MAIN_DOMAIN:-}" ]]; then
        error "缺少必需参数: --domain"
        show_help
        exit 1
    fi
    
    # 设置默认值
    ELEMENT_SUBDOMAIN="${ELEMENT_SUBDOMAIN:-element}"
    MATRIX_SUBDOMAIN="${MATRIX_SUBDOMAIN:-matrix}"
    MAS_SUBDOMAIN="${MAS_SUBDOMAIN:-mas}"
    RTC_SUBDOMAIN="${RTC_SUBDOMAIN:-rtc}"
    TURN_SUBDOMAIN="${TURN_SUBDOMAIN:-turn}"
    HTTPS_PORT="${HTTPS_PORT:-$DEFAULT_HTTPS_PORT}"
    FEDERATION_PORT="${FEDERATION_PORT:-$DEFAULT_FEDERATION_PORT}"
    SERVICE_DIR="${SERVICE_DIR:-$DEFAULT_SERVICE_DIR}"
    SSL_MODE="${SSL_MODE:-$DEFAULT_SSL_MODE}"
    WAN_INTERFACE="${WAN_INTERFACE:-ether1}"
}

# 验证环境
validate_environment() {
    info "验证部署环境..."
    
    # 检查Kubernetes连接
    if ! kubectl cluster-info &>/dev/null; then
        error "无法连接到Kubernetes集群，请检查kubeconfig配置"
        exit 1
    fi
    
    # 检查Helm
    if ! helm version &>/dev/null; then
        error "Helm未安装或配置不正确"
        exit 1
    fi
    
    # 检查Docker
    if ! docker version &>/dev/null; then
        error "Docker未安装或未启动"
        exit 1
    fi
    
    success "环境验证通过"
}

# 创建命名空间
create_namespace() {
    info "创建Kubernetes命名空间: $NAMESPACE"
    
    if kubectl get namespace "$NAMESPACE" &>/dev/null; then
        warning "命名空间 $NAMESPACE 已存在"
    else
        kubectl create namespace "$NAMESPACE"
        success "命名空间 $NAMESPACE 创建成功"
    fi
}

# 下载官方ESS-HELM Chart
download_ess_helm() {
    info "下载官方ESS-HELM Chart (版本: $ESS_HELM_VERSION)..."
    
    local chart_dir="$SERVICE_DIR/ess-helm"
    mkdir -p "$chart_dir"
    
    # 添加官方Helm仓库
    helm repo add element-hq https://element-hq.github.io/ess-helm
    helm repo update
    
    # 下载Chart
    helm pull element-hq/matrix-stack --version "$ESS_HELM_VERSION" --untar --untardir "$chart_dir"
    
    success "ESS-HELM Chart下载完成"
}

# 生成配置文件
generate_configurations() {
    info "生成部署配置文件..."
    
    local config_dir="$SERVICE_DIR/configs"
    mkdir -p "$config_dir"
    
    # 生成主配置文件
    generate_main_values "$config_dir/values.yaml"
    
    # 生成Router WAN IP检测配置
    generate_router_wan_ip_config "$config_dir/values-router-wan-ip-detection.yaml"
    
    # 生成虚拟公网IP路由配置
    generate_virtual_ip_config "$config_dir/values-virtual-public-ip-routing.yaml"
    
    # 生成外部服务器配置
    generate_external_server_config "$config_dir/values-external-server.yaml"
    
    success "配置文件生成完成"
}

# 生成主配置文件
generate_main_values() {
    local file_path="$1"
    
    cat > "$file_path" << EOF
# ESS-HELM 主配置文件
# 版本: $ESS_HELM_VERSION
# 模式: 外部服务器部署

global:
  serverName: "$MATRIX_SUBDOMAIN.$MAIN_DOMAIN"
  
# Element Web配置
elementWeb:
  enabled: true
  ingress:
    enabled: true
    hosts:
      - host: "$ELEMENT_SUBDOMAIN.$MAIN_DOMAIN"
        paths:
          - path: /
            pathType: Prefix

# Synapse配置
synapse:
  enabled: true
  serverName: "$MATRIX_SUBDOMAIN.$MAIN_DOMAIN"
  publicBaseurl: "https://$MATRIX_SUBDOMAIN.$MAIN_DOMAIN:$HTTPS_PORT"
  
  ingress:
    enabled: true
    hosts:
      - host: "$MATRIX_SUBDOMAIN.$MAIN_DOMAIN"
        paths:
          - path: /
            pathType: Prefix
  
  # 联邦配置
  federation:
    enabled: true
    port: $FEDERATION_PORT

# Matrix认证服务配置
matrixAuthenticationService:
  enabled: true
  ingress:
    enabled: true
    hosts:
      - host: "$MAS_SUBDOMAIN.$MAIN_DOMAIN"
        paths:
          - path: /
            pathType: Prefix

# Matrix RTC配置
matrixRtc:
  enabled: true
  ingress:
    enabled: true
    hosts:
      - host: "$RTC_SUBDOMAIN.$MAIN_DOMAIN"
        paths:
          - path: /
            pathType: Prefix

# HAProxy负载均衡器配置
haproxy:
  enabled: true
  
# PostgreSQL数据库配置
postgresql:
  enabled: true
  auth:
    postgresPassword: "$(openssl rand -base64 32)"
    database: "synapse"

# SSL/TLS配置
tls:
  mode: "$SSL_MODE"
  $(if [[ "$SSL_MODE" == "custom" ]]; then
    echo "  certFile: \"$SSL_CERT_PATH\""
    echo "  keyFile: \"$SSL_KEY_PATH\""
  fi)

EOF
}

# 生成Router WAN IP检测配置
generate_router_wan_ip_config() {
    local file_path="$1"
    
    cat > "$file_path" << EOF
# Router WAN IP自动检测配置
# 基于RouterOS API，5秒检测间隔

routerWanIpDetection:
  enabled: true
  mode: "wan-ip-only"
  
  router:
    host: "${ROUTER_IP:-***********}"
    username: "${ROUTER_USERNAME:-admin}"
    password: "${ROUTER_PASSWORD:-}"
    port: 8728
    useSSL: true
    timeout: 10
    
  detection:
    schedule: "*/5 * * * * *"  # 5秒检测间隔
    wanInterface: "$WAN_INTERFACE"
    
  validation:
    enableWanIpVerification: true
    disableExternalServices: true  # 完全本地化
    
  # 日志配置
  logging:
    level: "INFO"
    enableDebug: false

EOF
}

# 生成虚拟公网IP路由配置
generate_virtual_ip_config() {
    local file_path="$1"
    
    cat > "$file_path" << EOF
# 虚拟公网IP路由高可用配置
# 仅为需要外部直接连接的服务配置虚拟IP

virtualPublicIpRouting:
  enabled: true
  
  # LiveKit服务虚拟IP配置
  livekit:
    virtualPublicIp: "**********"
    enabled: true
    routingMode: "dynamic"
    healthCheck:
      enabled: true
      interval: "30s"
    
  # TURN服务虚拟IP配置
  turn:
    virtualPublicIp: "**********"
    enabled: true
    routingMode: "dynamic"
    healthCheck:
      enabled: true
      interval: "30s"
    
  # ESS内部服务自动处理，无需虚拟IP配置
  synapse:
    useVirtualIp: false  # 由ESS-HELM自动处理
  elementWeb:
    useVirtualIp: false  # 由ESS-HELM自动处理
  mas:
    useVirtualIp: false  # 由ESS-HELM自动处理
    
  # 路由管理配置
  routeManager:
    enabled: true
    updateInterval: "10s"
    failoverTimeout: "60s"
    
  # 零停机切换配置
  zeroDowntime:
    enabled: true
    gracefulSwitchTimeout: "30s"

EOF
}

# 生成外部服务器配置
generate_external_server_config() {
    local file_path="$1"
    
    cat > "$file_path" << EOF
# 外部服务器专用配置
# 针对公网访问环境的优化配置

externalServer:
  enabled: true
  
  # 网络配置
  networking:
    publicAccess: true
    loadBalancer:
      enabled: true
      type: "LoadBalancer"
    
  # 安全配置
  security:
    enableFirewall: true
    allowedPorts:
      - $HTTPS_PORT
      - $FEDERATION_PORT
      - 3478   # TURN
      - 5349   # TURN over TLS
    
  # 性能优化
  performance:
    enableCaching: true
    connectionPooling: true
    
  # 监控配置
  monitoring:
    enabled: true
    metrics:
      enabled: true
    logging:
      level: "INFO"

EOF
}

# 启动Router WAN IP检测服务
start_router_wan_ip_detection() {
    if [[ -n "${ROUTER_IP:-}" ]]; then
        info "启动Router WAN IP检测服务..."
        
        # 复制检测脚本
        cp "$PROJECT_DIR/scripts/router-wan-ip-detector.sh" "$SERVICE_DIR/"
        chmod +x "$SERVICE_DIR/router-wan-ip-detector.sh"
        
        # 启动检测服务
        nohup "$SERVICE_DIR/router-wan-ip-detector.sh" \
            --router-ip "$ROUTER_IP" \
            --router-username "$ROUTER_USERNAME" \
            --router-password "$ROUTER_PASSWORD" \
            --wan-interface "$WAN_INTERFACE" \
            > "$SERVICE_DIR/router-wan-ip-detection.log" 2>&1 &
        
        echo $! > "$SERVICE_DIR/router-wan-ip-detection.pid"
        success "Router WAN IP检测服务已启动"
    else
        warning "未配置Router信息，跳过WAN IP检测服务"
    fi
}

# 启动虚拟IP路由管理服务
start_virtual_ip_routing() {
    info "启动虚拟IP路由管理服务..."
    
    # 复制路由管理脚本
    cp "$PROJECT_DIR/scripts/virtual-public-ip-route-manager.sh" "$SERVICE_DIR/"
    chmod +x "$SERVICE_DIR/virtual-public-ip-route-manager.sh"
    
    # 启动路由管理服务
    nohup "$SERVICE_DIR/virtual-public-ip-route-manager.sh" \
        --livekit-ip "**********" \
        --turn-ip "**********" \
        > "$SERVICE_DIR/virtual-ip-routing.log" 2>&1 &
    
    echo $! > "$SERVICE_DIR/virtual-ip-routing.pid"
    success "虚拟IP路由管理服务已启动"
}

# 部署ESS-HELM
deploy_ess_helm() {
    info "开始部署ESS-HELM..."
    
    local config_dir="$SERVICE_DIR/configs"
    local chart_dir="$SERVICE_DIR/ess-helm/matrix-stack"
    
    # 执行Helm部署
    helm upgrade --install "$RELEASE_NAME" "$chart_dir" \
        --namespace "$NAMESPACE" \
        --values "$config_dir/values.yaml" \
        --values "$config_dir/values-router-wan-ip-detection.yaml" \
        --values "$config_dir/values-virtual-public-ip-routing.yaml" \
        --values "$config_dir/values-external-server.yaml" \
        --timeout 20m \
        --wait
    
    success "ESS-HELM部署完成"
}

# 验证部署
verify_deployment() {
    info "验证部署状态..."
    
    # 检查Pod状态
    kubectl get pods -n "$NAMESPACE"
    
    # 检查服务状态
    kubectl get services -n "$NAMESPACE"
    
    # 检查Ingress状态
    kubectl get ingress -n "$NAMESPACE"
    
    success "部署验证完成"
}

# 显示部署信息
show_deployment_info() {
    echo
    echo -e "${GREEN}部署完成！${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "Element Web: ${WHITE}https://$ELEMENT_SUBDOMAIN.$MAIN_DOMAIN:$HTTPS_PORT${NC}"
    echo -e "Matrix 服务器: ${WHITE}https://$MATRIX_SUBDOMAIN.$MAIN_DOMAIN:$HTTPS_PORT${NC}"
    echo -e "MAS 认证服务: ${WHITE}https://$MAS_SUBDOMAIN.$MAIN_DOMAIN:$HTTPS_PORT${NC}"
    echo -e "RTC 服务: ${WHITE}https://$RTC_SUBDOMAIN.$MAIN_DOMAIN:$HTTPS_PORT${NC}"
    echo -e "TURN 服务: ${WHITE}$TURN_SUBDOMAIN.$MAIN_DOMAIN${NC}"
    echo
    echo -e "服务目录: ${WHITE}$SERVICE_DIR${NC}"
    echo -e "配置文件: ${WHITE}$SERVICE_DIR/configs/${NC}"
    echo -e "日志文件: ${WHITE}$SERVICE_DIR/*.log${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo
    echo -e "${YELLOW}使用管理脚本进行后续管理:${NC}"
    echo -e "  ${WHITE}$PROJECT_DIR/scripts/admin.sh${NC}"
    echo
}

# 主函数
main() {
    echo -e "${CYAN}ESS-HELM 外部服务器部署脚本${NC}"
    echo -e "${CYAN}版本: $ESS_HELM_VERSION${NC}"
    echo
    
    # 解析参数
    parse_arguments "$@"
    
    # 验证环境
    validate_environment
    
    # 创建服务目录
    mkdir -p "$SERVICE_DIR"
    
    # 创建命名空间
    create_namespace
    
    # 下载ESS-HELM
    download_ess_helm
    
    # 生成配置文件
    generate_configurations
    
    # 启动辅助服务
    start_router_wan_ip_detection
    start_virtual_ip_routing
    
    # 部署ESS-HELM
    deploy_ess_helm
    
    # 验证部署
    verify_deployment
    
    # 显示部署信息
    show_deployment_info
}

# 错误处理
trap 'error "部署过程中发生错误"; exit 1' ERR

# 启动主程序
main "$@"
